package data

import (
	"context"
	"fmt"
	"testing"

	"github.com/Sider-ai/go-pkg/ginutil"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"

	"web-coder/internal/biz/quota"
	"web-coder/internal/conf"
	"web-coder/internal/log"
	"web-coder/internal/third_client"
)

func TestQuota(t *testing.T) {
	dataConf, logConf := getConfig(t)
	entClient, _, err := log.NewEntClient(dataConf)
	if err != nil {
		panic(err)
	}
	logger := log.NewSlogLogger(logConf, entClient)
	redisClient, _, err := third_client.NewRedisClient(dataConf)
	if err != nil {
		panic(err)
	}
	tokenClient, err := third_client.NewTokenClient()
	if err != nil {
		panic(err)
	}
	quotaClient, err := quota.NewUserQuotaClient(dataConf)
	if err != nil {
		panic(err)
	}

	ctx := context.WithValue(context.Background(), ginutil.CurrentUserInfoKey, &ginutil.UserInfo{
		UserID: 6834,
	})
	ctx = context.WithValue(ctx, ginutil.CallInfoKey, &ginutil.CallInfo{
		AppName: "global",
		TraceID: "test-web-code-lzh",
	})

	quotaManager := quota.NewQuotaManager(quotaClient, logger, redisClient, tokenClient)
	resp, err := quotaManager.GetUserQuotaInfo(ctx, 6834)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%+v\n", resp)

}

func TestDeductQuota(t *testing.T) {
	dataConf, logConf := getConfig(t)
	entClient, _, err := log.NewEntClient(dataConf)
	if err != nil {
		panic(err)
	}
	logger := log.NewSlogLogger(logConf, entClient)
	redisClient, _, err := third_client.NewRedisClient(dataConf)
	if err != nil {
		panic(err)
	}
	tokenClient, err := third_client.NewTokenClient()
	if err != nil {
		panic(err)
	}
	quotaClient, err := quota.NewUserQuotaClient(dataConf)
	if err != nil {
		panic(err)
	}

	ctx := context.WithValue(context.Background(), ginutil.CurrentUserInfoKey, &ginutil.UserInfo{
		UserID: 6834,
	})
	ctx = context.WithValue(ctx, ginutil.CallInfoKey, &ginutil.CallInfo{
		AppName: "global",
		TraceID: "test-web-code-lzh",
	})

	quotaManager := quota.NewQuotaManager(quotaClient, logger, redisClient, tokenClient)
	quotaManager.DeductQuotaChat(ctx, 6834, "qwen3-coder-plus", 3422, 4322, 1280, 0, 0)
}

func getConfig(t *testing.T) (*conf.Data, *conf.Log) {
	configPath := "/Users/<USER>/project/sider/web-coder/configs/local.config.yaml"

	c := config.New(
		config.WithSource(
			file.NewSource(configPath),
		),
	)
	defer c.Close()

	if err := c.Load(); err != nil {
		t.Fatalf("failed to load config file %s: %v", configPath, err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		t.Fatalf("failed to scan config: %v", err)
	}

	if bc.Data == nil {
		t.Fatalf("config data section is nil")
	}
	if bc.Log == nil {
		t.Fatalf("config log section is nil")
	}
	return bc.Data, bc.Log
}
