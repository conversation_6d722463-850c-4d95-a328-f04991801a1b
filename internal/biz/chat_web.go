package biz

import (
	"context"
	"encoding/json"
	"log/slog"
	"regexp"
	"strings"
	"time"
	"web-coder/api"
	v1 "web-coder/api/chat_web/v1"
	"web-coder/internal/third_client"

	"github.com/Sider-ai/go-pkg/siderfile"
	"github.com/elliotchance/pie/v2"

	"github.com/Sider-ai/go-pkg/ginutil"
	"github.com/Sider-ai/go-pkg/streamtagparser"
	"github.com/Sider-ai/go-pkg/userquota"

	"web-coder/internal/biz/util"

	"github.com/Sider-ai/go-pkg/documentmodel"
	"github.com/Sider-ai/go-pkg/siderasyncagent"
	"github.com/Sider-ai/go-pkg/siderllm"
	sidererrors "github.com/Sider-ai/sider-errors"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson/primitive"
	//"web-coder/internal/biz/util"
)

const (
	ChatMessageTypeText                = "text"
	ChatMessageTypeImage               = "image"
	ChatMessageTypeSearch              = "search"
	ChatMessageTypeToolCall            = "tool_call"
	ChatMessageTypeToolCallsStream     = "tool_calls_stream"
	ChatMessageTypeErr                 = "error"
	ChatMessageTypeRagFiles            = "rag_files"
	ChatMessageTypeCreatedConversation = "created_conversation"
	ChatMessageTypeCreatedMessage      = "created_message"
	ChatMessageTypeUserQuota           = "user_quota"
	ChatMessageTypeClear               = "clear"
	ChatMessageTypeReasoning           = "reasoning"
	ChatMessageTypeCreateWisebase      = "create_wisebase"
	ChatMessageTypeWrite               = "write"
	ChatMessageTypeMultiContent        = "multi_content"
	ChatMessageTypeTag                 = "tag"
)

const (
	ModelNameSider           = "sider"
	ModelNameDeepseek        = "deepseek-chat"
	ModelNameGpt4oMini       = "gpt-4o-mini"
	ModelNameGemini          = "gemini-2.0-flash"
	ModelNameClaudeThinking  = "claude-3.7-sonnet-think"
	ModelNameClaude4         = "claude-4-sonnet"
	ModelNameClaude4Thinking = "claude-4-sonnet-think"
	ModelNameGptO3           = "o3"
	ModelNameGemini25Pro     = "gemini-2.5-pro"
	ModelNameGpt4o           = "gpt-4o"
	ModelNameGpt4Dot1        = "gpt-4.1"
	ModelNameGpt4Dot1Mini    = "gpt-4.1-mini"
	ModelNameQwen3CoderPlus  = "qwen3-coder-plus"
	ModelNameKimiK2          = "kimi-k2"
	ModelNameGLM4Dot5        = "glm-4.5"
	ModelNameGPT5            = "gpt-5"
	ModelNameGPT5Think       = "gpt-5-think"
)

type CreatedConversation string
type CreatedMessage string
type ClearData string

type ChatRequest struct {
	Model             string
	ChatHistoryID     string
	Save              bool
	HiddenModelReturn bool
	MaxRequestToken   int
	MaxToolCallCount  int
	ConsumeCredit     bool
	// Scene             siderllm.LLMScene
	ChatPDF bool // 和文件聊天
	UserID  int

	// 暂存的数据
	Tools             []siderllm.ToolSchema
	MessageList       []*ChatMessage //历史聊天记录
	RequestMessage    *ChatMessage   //用户的消息
	AIMessage         *ChatMessage   //ai补全的消息
	ParentMessage     *ChatMessage   //父消息
	UserParentMessage *ChatMessage

	// LLMFiles          []LLMFile
	// TextReturn        atomic.Bool // 是否有数据返回
	// Wisebase          *Wisebase
	MaxOutputToken int64
}

//后期考虑迁到history文件

type ChatMessage struct {
	ID                 string                         // 消息唯一标识符
	Model              string                         // 模型名称
	ActualModel        string                         // 实际使用的模型名称
	Role               documentmodel.ChatMessageRole  // 消息角色（用户、助手,tool；等）
	Hidden             bool                           // 是否隐藏消息
	MultiContent       []documentmodel.MultiContent   // 多媒体内容数组
	UserMultiContent   []documentmodel.MultiContent   // 用户多媒体内容数组
	Token              int                            // 令牌数量
	Status             documentmodel.MessageStatus    // 消息状态
	Quote              *documentmodel.Quote           // 引用信息
	ErrMsg             string                         // 错误消息
	AppName            string                         // 应用名称
	AppVersion         string                         // 应用版本
	TraceID            string                         // 跟踪ID
	TimeZone           string                         // 时区信息
	Level              int                            // 消息级别
	ParentIDS          []string                       // 父消息ID数组
	ChatConversationID string                         // 聊天会话ID
	CreatedAt          time.Time                      // 创建时间
	ParentID           string                         // 父消息ID
	PromptTemplates    []documentmodel.PromptTemplate // 提示模板数组
	ToolsParam         *documentmodel.ToolsParam      // 工具参数
	ResponseFormat     *documentmodel.ResponseFormat  // 响应格式
	LibraryOption      *documentmodel.LibraryOption   // 库选项
	AsyncTask          *documentmodel.AsyncTask       // 异步任务
	CurrentChildID     primitive.ObjectID             // 当前子消息ID
	UserQuotaInfo      *documentmodel.UserQuotaInfo   // 用户配额信息
	CreditType         userquota.CreditType           // 信用点类型
	CreditCount        int                            // 信用点数量
	IsTrialCredit      bool                           // 是否是试用次数消息
	SubMessages        []documentmodel.SubMessage     // 子消息数组
}

type ChatUserRequest struct {
	FunctionCall     []*v1.MultiContent
	ChatHistoryID    string
	Model            string
	NeedHistory      bool
	Hidden           bool
	MaxRequestToken  int
	ParentID         string
	UserParentID     string
	Prompt           []*v1.MultiContent
	PromptTemplates  []documentmodel.PromptTemplate
	ImageSiderFileID map[string]struct{}
	//ResourceIds                    []string //引用资源
	//ResourceTypes                  []model.ResourceType
	FileIDs                        []string
	WisebaseId                     string
	ToolsParam                     *siderllm.ToolsParam
	ResponseFormat                 *siderllm.ResponseFormat
	Quote                          *documentmodel.Quote
	NoResourceDeleteTemplatePrompt []string
	CheckAll                       bool
	UserID                         int
}

func (o *ChatRequest) GetActualModel() string {
	if o.Model == ModelNameSider {
		return ModelNameGemini
	}
	if o.Model == "deepseek" {
		return ModelNameDeepseek
	}

	return o.Model
}

type ChatWebBiz struct {
	chatClient      *third_client.ChatClient
	ossClient       *third_client.OSSClients
	repo            IChatHistoryRepo
	siderFileClient *siderfile.Client
}

func NewChatWebBiz(
	chatClient *third_client.ChatClient,
	repo IChatHistoryRepo,
	siderFileClient *siderfile.Client,
	ossClient *third_client.OSSClients,
) *ChatWebBiz {
	return &ChatWebBiz{
		repo:            repo,
		siderFileClient: siderFileClient,
		ossClient:       ossClient,
		chatClient:      chatClient,
	}
}

func (o *ChatWebBiz) FunctionCallResult(ctx context.Context, in *v1.FunctionCallResultRequest) error {

	chatCustomToolRequest := &siderllm.ChatCustomToolRequest{
		CallID: in.CallID,
		Result: &siderllm.CustomToolResult{
			Code:    int(in.Result.Code),
			Message: in.Result.Message,
			Data: &siderllm.CustomToolData{
				IsError: in.Result.Data.IsError,
			},
		},
	}
	var customToolDataContentList []siderllm.CustomToolDataContent

	for _, content := range in.Result.Data.Content {
		customToolDataContentList = append(customToolDataContentList, siderllm.CustomToolDataContent{
			Type: siderllm.CustomToolDataType(content.Type),
			Text: "Installation successful",
		})

	}
	chatCustomToolRequest.Result.Data.Content = customToolDataContentList
	err := o.chatClient.Cli.SetCustomToolResult(ctx, chatCustomToolRequest)
	if err != nil {
		return err
	}
	return nil
}

// 处理与LLM的聊天请求并处理可能的失败重试
// 参数:
// - ctx: 上下文，用于传递请求的元数据和控制请求的生命周期
// - in: 聊天请求参数，包含模型选择、消息内容等信息
// - notifyCh: 字节切片通道，用于向客户端发送SSE消息
// 返回:
// - requestModel: 实际使用的请求模型名称
// - resp: LLM的响应消息
// - err: 如果有错误发生则返回，否则返回nil
func (o *ChatWebBiz) siderChat(ctx context.Context, in *ChatRequest, notifyCh chan []byte) (requestModel string, resp *siderllm.MessageResp, err error) {
	// 创建聊天流请求对象
	var req *siderllm.ChatStreamRequest
	req, err = o.createChatStreamRequest(ctx, notifyCh, in)
	if err != nil {
		// 如果创建请求失败，将错误包装为无效参数错误
		err = sidererrors.InvalidParams.WithCause(err)
		return
	}

	// 记录实际使用的请求模型
	requestModel = req.Model

	req.Platform = o.getModelPlatform(req)
	req.ThinkOpt = &siderllm.ThinkOpt{
		BudgetTokens: 1024,
	}
	// 发送聊天流请求到LLM服务
	//req.AddMultiMessage()
	resp, err = o.chatClient.Cli.ChatStream(ctx, req)

	// 如果请求成功，直接返回结果
	if err != nil {
		return
	}
	//
	//将模型切换为GPT-4o-mini进行重试
	//req.Model = ModelNameGpt4oMini
	//// 使用新模型重新发送聊天请求
	//resp, err = o.chatClient.Cli.ChatStream(ctx, req)
	return
}

func (o *ChatWebBiz) getModelPlatform(req *siderllm.ChatStreamRequest) string {
	var platform string
	switch req.Model {
	case ModelNameQwen3CoderPlus:
		platform = "Aliyun"
	case ModelNameGpt4Dot1Mini, ModelNameGpt4Dot1, ModelNameGpt4o, ModelNameGpt4oMini, ModelNameGptO3, ModelNameGPT5, ModelNameGPT5Think:
		platform = "Openai"
	case ModelNameClaude4, ModelNameClaude4Thinking, ModelNameClaudeThinking:
		platform = "Anthropic"
	case ModelNameGemini, ModelNameGemini25Pro:
		platform = "Gemini"
	case ModelNameKimiK2:
		platform = "Road2All"
	case ModelNameGLM4Dot5:
		platform = "Zhipu"
	default:
		platform = "Road2All"
	}
	return platform
}

// ChatWithLLM 处理与LLM的聊天请求并返回响应

func (o *ChatWebBiz) ChatWithLLM(ctx context.Context, in *ChatRequest, notifyCh chan []byte) (*siderllm.MessageResp, *errors.Error) {

	// 在数据库中创建聊天消息记录（包括用户消息，ai消息的创建）
	err := o.createChatMessage(ctx, in)
	// 如果创建消息失败，返回错误
	if err != nil {
		return nil, api.ErrorCreateOneFailed("Create The Chat Message Failed.")
	}

	// 向客户端发送消息创建成功的通知
	o.SendDataToClient(ctx, in, notifyCh, CreatedMessage(""), nil)
	var (
		chatResp     *siderllm.MessageResp // LLM的响应消息
		requestModel string                // 实际使用的模型名称
	)
	// 调用LLM
	requestModel, chatResp, err = o.siderChat(ctx, in, notifyCh) // Assuming siderChat performs the actual LLM call
	if chatResp != nil {
		var transErr error
		transErr = o.transformCodePlaceholdersList(chatResp.FullContents)
		if transErr != nil {
			return nil, api.ErrorGetOneFailed(transErr.Error())
		}
	}

	// 处理聊天结果并返回
	return chatResp, o.handleChatResult(ctx, in, &chatResult{
		chatResp:     chatResp,     // LLM响应
		requestModel: requestModel, // 使用的模型
	}, notifyCh, err)
}

type chatResult struct {
	chatResp     *siderllm.MessageResp
	requestModel string
	// task         *documentmodel.AsyncTask
	subMessage *documentmodel.SubMessage
}

func (o *ChatWebBiz) handleChatResult(ctx context.Context, in *ChatRequest, chatResult *chatResult, notifyCh chan []byte, err error) *errors.Error {

	// 创建一个用于更新AI消息的结构体
	updateAiMessage := ChatMessage{
		// AsyncTask: chatResult.task,
	}

	if err != nil {
		// 如果有错误，从错误中提取SiderError
		siderErr := sidererrors.FromError(err)
		// 判断错误类型是否为取消请求相关的错误
		if sidererrors.LLMRequestCanceled.Is(siderErr) || sidererrors.RequestCanceled.Is(siderErr) || strings.Contains(siderErr.Error(), "context canceled") {
			// 如果是取消请求，将消息状态设置为已停止
			updateAiMessage.Status = documentmodel.MessageStatusStopped
		} else {
			// 其他错误，将消息状态设置为错误
			updateAiMessage.Status = documentmodel.MessageStatusError
			// 保存错误信息
			updateAiMessage.ErrMsg = err.Error()

			// 检查多内容中是否已经包含错误类型的内容
			if len(lo.Filter(updateAiMessage.MultiContent, func(item documentmodel.MultiContent, index int) bool {
				return item.Type == documentmodel.ContentTypeError
			})) == 0 {
				// 如果没有错误类型的内容，添加一个错误内容
				updateAiMessage.MultiContent = append(updateAiMessage.MultiContent, documentmodel.MultiContent{
					Type: documentmodel.ContentTypeError,
					Error: &documentmodel.ChatError{
						Code:     siderErr.Code,
						Message:  siderErr.Message,
						HttpCode: siderErr.HttpCode,
					},
				})
			}
		}
		// 设置模型信息
		updateAiMessage.Model = in.Model
		updateAiMessage.ActualModel = chatResult.requestModel
		// 如果有聊天响应，转换其内容
		if chatResult.chatResp != nil {
			updateAiMessage.MultiContent = o.convertSiderLLMFullContent(in, chatResult.chatResp.FullContents)
		}
	} else {
		if chatResult.chatResp != nil {
			// 转换聊天响应的内容
			updateAiMessage.MultiContent = o.convertSiderLLMFullContent(in, chatResult.chatResp.FullContents)
			// 设置实际使用的模型
			updateAiMessage.ActualModel = chatResult.chatResp.GeneralInfo.Model
		}
		// 设置消息状态为成功
		updateAiMessage.Status = documentmodel.MessageStatusSuccess
		// 设置请求的模型
		updateAiMessage.Model = in.Model
		// 处理成功响应（如需要，进行日志记录、最终客户端通知等）
		//if chatResult.chatResp != nil {
		//}
	}
	if in.Save {
		// 创建一个带超时的上下文用于保存操作
		saveContext, saveCancel := context.WithTimeout(context.Background(), time.Second*30)
		defer saveCancel()
		// 如果有子消息

		// 更新主聊天完成的会话
		if saveErr := o.chatCompletedConversation(saveContext, in, updateAiMessage); saveErr != nil {
			// 如果保存失败，向客户端发送错误信息
			o.SendDataToClient(ctx, in, notifyCh, nil, api.ErrorUpdateOneFailed("update message failed %v", err))
		}

	}
	// 如果有错误，转换为SiderLLM错误并返回
	if err != nil {
		return util.ConvertSiderLLMError(err)
	}

	return nil
}

// chatCompletedConversation 处理聊天完成后的会话保存逻辑
// 参数:
// - ctx: 上下文，包含请求的元数据和控制请求的生命周期
// - in: 聊天请求参数，包含用户输入、模型选择等信息
// - aiMessage: AI生成的消息内容
// 返回:
// - error: 如果有错误发生则返回，否则返回nil
func (o *ChatWebBiz) chatCompletedConversation(ctx context.Context, in *ChatRequest, aiMessage ChatMessage) error {
	// 检查请求消息和AI消息是否存在，如果不存在则直接返回
	if in.RequestMessage == nil || in.AIMessage == nil {
		return nil
	}
	// 将AI消息的多内容格式转换为单一文本字符串
	// 使用lo.Reduce函数遍历所有内容项并合并文本内容
	aiMessageContents := lo.Reduce(aiMessage.MultiContent, func(agg string, item documentmodel.MultiContent, index int) string {
		// 初始化一个空字符串用于存储当前内容项的文本
		var str string
		// 如果内容类型是文本，则获取其文本内容
		if item.Type == documentmodel.ContentTypeText {
			str = item.Text
		}
		// 如果当前内容项没有文本，则返回累加器的值
		if str == "" {
			return agg
		}
		// 如果累加器为空，则直接返回当前文本
		if agg == "" {
			return str
		} else {
			// 否则将当前文本添加到累加器后面，用换行符分隔
			return agg + "\n" + str
		}
	}, "")

	// 将聊天历史ID转换为MongoDB的ObjectID
	cid, _ := primitive.ObjectIDFromHex(in.ChatHistoryID)
	// 将AI消息ID转换为MongoDB的ObjectID
	amid, _ := primitive.ObjectIDFromHex(in.AIMessage.ID)
	// 创建聊天完成保存数据结构
	data := &ChatCompletedSave{
		// 截取AI消息内容的前256个字符作为描述
		Description: lo.Substring(aiMessageContents, 0, 256),
		// 设置会话ID
		ConversationID: cid,
		// 设置AI消息的详细信息
		AIMessage: documentmodel.ChatMessage{
			Model:              aiMessage.Model,        // 使用的模型名称
			ActualModel:        aiMessage.ActualModel,  // 实际使用的模型名称
			MultiContent:       aiMessage.MultiContent, // 多内容格式的消息内容
			Status:             aiMessage.Status,       // 消息状态
			Token:              aiMessage.Token,        // 消息的token信息
			ErrMsg:             aiMessage.ErrMsg,       // 错误信息（如果有）
			ChatConversationID: cid,                    // 会话ID
			AsyncTask:          aiMessage.AsyncTask,    // 异步任务信息（如果有）
		},
		// 设置AI消息ID
		AIMessageID: amid,
	}

	// 调用存储库的ChatCompleted方法保存聊天完成数据
	return o.repo.ChatCompleted(ctx, data)
}

// convertSiderLLMFullContent 将 siderllm 的 FullContent 转换为 documentmodel 的 MultiContent
func (o *ChatWebBiz) convertSiderLLMFullContent(in *ChatRequest, contents []siderllm.FullContent) []documentmodel.MultiContent {
	// 将 siderllm 的 FullContent 转换为 documentmodel 的 MultiContent
	multiContents := documentmodel.ConvertFromFullContent(contents)

	// 返回处理后的多内容列表
	return multiContents
}

func (o *ChatWebBiz) addChatMessage(chatReq *siderllm.ChatRequest, role documentmodel.ChatMessageRole, multiContent []documentmodel.MultiContent, token int, needCache bool) {
	item, _ := documentmodel.ConvertToMessageContentList(&documentmodel.ConvertToMessageContentParam{
		MultiList:   multiContent,
		ImageDetail: "auto",
		ObjectKeyToUrl: func(objKey string) string {
			//signUrl, _, _ := o.ossRepo.PreSignFileURLFromOss(objKey)
			client := o.ossClient.GetFileClient()
			signUrl, err := client.SignUrl(objKey, time.Hour)
			if err != nil {
				slog.Error("Image signature failed err：", err)
			}
			return signUrl
		},
	})
	opts := []siderllm.MessageOption{
		siderllm.MultiContent(item), siderllm.MessageTokenOption(token),
	}
	if needCache {
		opts = append(opts, siderllm.NeedCacheOption(true))
	}

	chatReq.AddMessage(string(role), opts...)

}

// 处理资源ID选择
// checkAll: true-全选模式(返回排除的ID), false-部分选择模式(返回选中的ID)
func getSelectedResourceIds(
	resourceType documentmodel.LibraryResourceType,
	checkAll bool,
	ids []primitive.ObjectID,
) documentmodel.LibraryResource {
	return documentmodel.LibraryResource{
		Type:     resourceType,
		CheckAll: checkAll,
		Ids:      ids,
	}
}

func (o *ChatWebBiz) CheckAndSetRequest(ctx context.Context, in *ChatUserRequest) (*ChatRequest, *errors.Error) {
	chatReq := &ChatRequest{
		ChatHistoryID: in.ChatHistoryID,
		Model:         in.Model,
		UserID:        in.UserID,
	}

	//获取历史聊天和当前请求
	var err error

	//获取聊天历史记录
	chatReq.ParentMessage, chatReq.RequestMessage, chatReq.MessageList, err = o.getChatMessageList(ctx, in)
	if err != nil {
		// 如果获取消息列表失败，返回错误
		return nil, api.ErrorGetListFailed("Get The Chat Message History Failed.")
	}
	// 转换提示模板，将代码和附件内容和system prompt拼接起来
	if err = o.convertPromptTemplates(ctx, chatReq); err != nil {
		return nil, v1.ErrorChatPromptTemplatesInvalid(err.Error())
	}
	return chatReq, nil
}

func (o *ChatWebBiz) sendTaskDataToClient(ctx context.Context, in *ChatRequest, notifyCh chan []byte, content any, task *siderasyncagent.ChatStreamAsyncData, err *errors.Error) {
	var res *v1.ChatMessageResp
	if err != nil {
		res = &v1.ChatMessageResp{
			Type:  ChatMessageTypeErr,
			Error: err.Reason,
			Code:  err.Code,
		}
	} else {
		res = o.newCompletionResp(ctx, content, in)
	}
	var bs []byte
	if res == nil {
		bs = []byte{}
	} else {
		bs, _ = json.Marshal(res)
	}

	notifyCh <- bs
}

// SendDataToClient 将数据发送给客户端
// 参数:
// - ctx: 上下文，包含请求的元数据和控制请求的生命周期
// - in: 聊天请求参数，包含用户输入、模型选择等信息
// - notifyCh: 字节切片通道，用于向客户端发送SSE消息
// - content: 任意类型的内容，将被转换为客户端可理解的响应格式
// - err: 错误信息，如果不为nil，则会生成错误响应
// 功能:
// 1. 根据输入内容生成客户端响应对象
// 2. 如果有错误，创建错误类型的响应
// 3. 如果没有错误，调用newCompletionResp将内容转换为标准响应格式
// 4. 将响应对象序列化为JSON
// 5. 通过notifyCh通道发送给客户端
func (o *ChatWebBiz) SendDataToClient(ctx context.Context, in *ChatRequest, notifyCh chan []byte, content any, err *errors.Error) {
	var res *v1.ChatMessageResp
	if err != nil {
		// 如果有错误，创建错误类型的响应
		res = &v1.ChatMessageResp{
			Type:  ChatMessageTypeErr, // 设置消息类型为错误
			Error: err.Reason,         // 设置错误原因
			Code:  err.Code,           // 设置错误代码
		}
	} else {
		// 如果没有错误，将内容转换为标准响应格式
		res = o.newCompletionResp(ctx, content, in)
	}

	var bs []byte
	if res == nil {
		// 如果响应为空，发送空字节数组
		bs = []byte{}
	} else {
		// 将响应对象序列化为JSON
		bs, _ = json.Marshal(res)
	}

	// 通过通道发送序列化后的响应
	notifyCh <- bs
}

// createChatStreamRequest 创建聊天流请求对象
// 参数:
// - ctx: 上下文，包含请求的元数据和控制请求的生命周期
// - notifyCh: 字节切片通道，用于向客户端发送SSE消息
// - in: 聊天请求参数，包含用户输入、模型选择等信息
// 返回:
// - *siderllm.ChatStreamRequest: 配置好的聊天流请求对象
// - error: 如果创建过程中发生错误则返回，否则返回nil
// 功能:
// 1. 调用createChatRequest创建基础聊天请求对象
// 2. 构建ChatStreamRequest对象，包含各种回调函数处理不同类型的流式响应
// 3. 每个回调函数接收特定类型的数据并通过SendDataToClient发送给客户端
func (o *ChatWebBiz) createChatStreamRequest(ctx context.Context, notifyCh chan []byte, in *ChatRequest) (*siderllm.ChatStreamRequest, error) {
	// 创建基础聊天请求对象
	chatRequest, err := o.createChatRequest(ctx, in)
	if err != nil {
		return nil, err
	}

	// 构建聊天流请求对象，包含各种回调函数
	req := &siderllm.ChatStreamRequest{
		ChatRequest: *chatRequest,
		// 处理文本类型的流式响应
		OnText: func(r siderllm.ChatStreamTextData) {
			o.SendDataToClient(ctx, in, notifyCh, r, nil)
		},
		// 处理标签流类型的响应
		OnTagStream: func(data siderllm.ChatStreamTagStreamData) {
			o.SendDataToClient(ctx, in, notifyCh, data, nil)
		},
		// 处理工具调用类型的响应
		OnToolCall: func(data siderllm.ChatStreamToolCallData) {
			o.SendDataToClient(ctx, in, notifyCh, data, nil)
		},
		// 处理工具调用流类型的响应
		OnToolCallStream: func(data siderllm.ChatStreamToolCallStreamData) {
			o.SendDataToClient(ctx, in, notifyCh, data, nil)
		},
		OnSearch: func(data siderllm.ChatStreamSearchData) {
			o.SendDataToClient(ctx, in, notifyCh, data, nil)
		},
		OnReasoning: func(data siderllm.ChatStreamReasoningContent) {
			o.SendDataToClient(ctx, in, notifyCh, data, nil)
		},
	}

	return req, nil
}

// newCompletionResp 将内容转换为标准响应格式

func (o *ChatWebBiz) newCompletionResp(ctx context.Context, content any, in *ChatRequest) *v1.ChatMessageResp {
	// 创建一个新的聊天消息响应对象
	resp := &v1.ChatMessageResp{
		// 设置默认消息类型为文本
		Type: ChatMessageTypeText,
		// 设置聊天历史ID
		ChatHistoryID: in.ChatHistoryID,
	}
	// 如果AI消息存在，设置消息ID
	if in.AIMessage != nil {
		resp.MessageID = in.AIMessage.ID
	}
	// 如果请求消息存在，设置请求消息ID
	if in.RequestMessage != nil {
		resp.ReqMessageID = in.RequestMessage.ID
	}

	// 如果不隐藏模型返回，则设置模型名称
	if !in.HiddenModelReturn {
		resp.Model = in.Model
	}
	// 根据内容类型进行不同处理
	switch val := content.(type) {
	case CreatedConversation:
		// 如果是创建会话类型，设置响应类型
		resp.Type = ChatMessageTypeCreatedConversation
	case CreatedMessage:
		// 如果是创建消息类型，设置响应类型
		resp.Type = ChatMessageTypeCreatedMessage
	case ClearData:
		// 如果是清除数据类型，设置响应类型
		resp.Type = ChatMessageTypeClear
	case siderllm.ChatStreamTextData:
		// 如果是文本流数据，设置响应类型和文本内容
		resp.Type = ChatMessageTypeText
		resp.Text = val.Text
	case siderllm.ChatStreamWrite:
		// 如果是写入流数据，设置类型为写入并填充状态
		resp.Type = ChatMessageTypeWrite
		resp.Write = &v1.ChatMessageResp_Write{
			Status: val.Status,
		}

	case siderllm.ChatStreamToolCallData:
		// 如果是工具调用数据，设置响应类型和工具调用信息
		resp.Type = ChatMessageTypeToolCall
		// 声明字节切片用于存储工具调用数据
		var bs []byte
		// 如果工具调用有数据，则将其序列化为JSON
		if val.ToolCall.Data != nil {
			bs, _ = json.Marshal(val.ToolCall.Data)
		}
		// 设置工具调用响应
		resp.ToolCall = &v1.ChatMessageResp_ToolCall{
			Name:   val.ToolCall.Name,
			Id:     val.ToolCall.ID,
			Status: val.ToolCall.Status,
			Data:   bs,
		}
	case siderllm.ChatStreamToolCallStreamData:
		// 如果是工具调用流数据，设置响应类型
		resp.Type = ChatMessageTypeToolCallsStream

		// 创建工具调用流数组
		var toolCalls []*v1.ChatMessageResp_ToolCallStream
		// 遍历所有工具调用流
		for _, v := range val.ToolCallStream {
			// 声明索引指针
			var index *int32
			// 如果有索引值，则转换为int32类型
			if v.Index != nil {
				temIndex := int32(*v.Index)
				index = &temIndex
			}
			// 添加工具调用流到数组
			toolCalls = append(toolCalls, &v1.ChatMessageResp_ToolCallStream{
				Id:    v.ID,
				Index: index,
				Type:  v.Type,
				Function: &v1.ChatMessageResp_ToolCallStream_FunctionCall{
					Arguments: v.Function.Arguments,
					Name:      v.Function.Name,
				},
			})
		}
		// 设置工具调用流响应
		resp.ToolCallStream = toolCalls
	case siderllm.ChatStreamImageData:
		// 如果是图片流数据，设置类型为图片
		resp.Type = ChatMessageTypeImage
		// 创建图片数组
		var images []*v1.ChatMessageResp_Image
		// 遍历所有图片数据
		for _, v := range val.Image {
			// 添加图片信息到数组
			images = append(images, &v1.ChatMessageResp_Image{
				ObjectKey: v.ObjectKey,
				Height:    int32(v.Height),
				Width:     int32(v.Width),
			})
		}
		// 设置图片数组
		resp.Image = images
		//搜索流
	case siderllm.ChatStreamSearchData:
		// 如果是搜索流数据，设置类型为搜索
		resp.Type = ChatMessageTypeSearch
		// 创建搜索数据对象
		search := &v1.SearchData{
			SearchKeywords: val.Search.SearchKeywords,
			SearchSnippets: make(map[int32]*v1.Snippet),
		}
		// 遍历所有搜索片段
		for i, v := range val.Search.SearchSnippets {
			// 添加搜索片段到映射
			search.SearchSnippets[int32(i)] = &v1.Snippet{
				Link:    v.Link,
				Title:   v.Title,
				Snippet: v.Snippet,
				Index:   int32(v.Index),
			}
		}
		// 设置搜索数据
		resp.SearchData = search
	case siderllm.ChatStreamUserQuotaData:
		// 定义一个内部函数，用于转换积分信息
		convertCreditInfo := func(info userquota.CreditInfo) *v1.ChatMessageResp_UserQuota_CreditInfo {
			return &v1.ChatMessageResp_UserQuota_CreditInfo{
				Period:      string(info.Period),
				ResetTime:   int32(info.ResetTime),
				Total:       int32(info.Total),
				Used:        int32(info.Used),
				Remain:      int32(info.Remain),
				ExtraTotal:  int32(info.ExtraTotal),
				ExtraRemain: int32(info.ExtraRemain),
			}
		}
		// 定义一个内部函数，用于转换功能限制信息
		convertFeatureLimit := func(info userquota.FeatureLimit) *v1.ChatMessageResp_UserQuota_FeatureUsedLimit_FeatureLimit {
			return &v1.ChatMessageResp_UserQuota_FeatureUsedLimit_FeatureLimit{
				VideoSummary:       int32(info.VideoSummary),
				ChatFile:           int32(info.ChatFile),
				Ocr:                int32(info.OCR),
				Painter:            int32(info.Painter),
				Tts:                int32(info.TTS),
				ShortVideo:         int32(info.ShortVideo),
				DataAnalysis:       int32(info.DataAnalysis),
				DeepResearch:       int32(info.DeepResearch),
				DeepResearchVisual: int32(info.DeepResearchVisual),
				WebCodeFreeTrial:   int32(info.WebCodeFreeTrial),
			}
		}

		// 如果是用户配额数据，设置类型为用户配额
		resp.Type = ChatMessageTypeUserQuota
		// 设置用户配额信息
		resp.UserQuota = &v1.ChatMessageResp_UserQuota{
			UserLevel:      string(val.UserQuota.UserLevel),
			IsTrial:        val.UserQuota.IsTrial,
			BasicCredit:    convertCreditInfo(val.UserQuota.BasicCredit),
			AdvancedCredit: convertCreditInfo(val.UserQuota.AdvancedCredit),
			FeatureUsed: &v1.ChatMessageResp_UserQuota_FeatureUsedLimit{
				Total: convertFeatureLimit(val.UserQuota.FeatureUsed.Total),
				Used:  convertFeatureLimit(val.UserQuota.FeatureUsed.Used),
			},
		}

	case siderllm.ChatStreamReasoningContent:
		// 如果是推理内容，设置响应类型和文本
		resp.Type = ChatMessageTypeReasoning
		resp.Text = val.ReasoningContent
	case siderasyncagent.ChatStreamWriteStatus:
		// 如果是写入状态流，设置类型为写入
		resp.Type = ChatMessageTypeWrite
		resp.Write = &v1.ChatMessageResp_Write{
			Status: val.Status.String(),
		}
	case []*v1.MultiContent:
		// 如果是多内容数组，设置类型为多内容
		resp.Type = ChatMessageTypeMultiContent
		resp.MultiContent = val
	case siderllm.ChatStreamTagStreamData:
		// 如果是标签流数据
		if val.TagStream.Type == streamtagparser.TagStreamTypeText {
			// 如果标签类型是文本，设置类型为文本
			resp.Type = ChatMessageTypeText
			resp.Text = val.TagStream.Text
		} else {
			// 否则设置类型为标签
			resp.Type = ChatMessageTypeTag
			resp.Tag = &v1.ChatMessageResp_Tag{
				Type: string(val.TagStream.Type),
				ID:   val.TagStream.TagID,
				Name: val.TagStream.TagName,
				// 转换标签属性
				Attrs: lo.Map(val.TagStream.Attrs, func(item streamtagparser.TagAttr, index int) *v1.ChatMessageResp_Tag_Attr {
					return &v1.ChatMessageResp_Tag_Attr{
						Name:  item.Name,
						Value: item.Value,
					}
				}),
				Content: val.TagStream.Content,
			}
		}
	default:
		// 如果是未知类型，记录错误日志
		slog.ErrorContext(ctx, "service.completion.newCompletionResp received unknown content type,content", slog.Any("content", content))
	}

	// 返回格式化后的响应对象
	return resp
}

// createChatRequest 创建聊天请求对象
func (o *ChatWebBiz) createChatRequest(ctx context.Context, in *ChatRequest) (*siderllm.ChatRequest, error) {
	// 创建并初始化聊天请求对象，设置基本参数
	chatRequest := &siderllm.ChatRequest{
		// Model:            in.GetActualModel(),            // 获取实际使用的模型名称
		Model: in.Model,
		// Scene:            in.Scene,                       // 设置聊天场景
		MaxToolCallCount: in.MaxToolCallCount, // 设置最大工具调用次数
		MaxRequestToken:  in.MaxRequestToken,  // 设置最大请求令牌数
		//ForwardingMode:   false,               //todo 使用前端传的转发模式
		Tools: in.Tools, // 设置可用工具列表(重点是前端执行的工具)
		Conversation: &siderllm.Conversation{ // 设置会话信息
			HashID: in.ChatHistoryID, // 设置聊天历史ID作为会话标识
		},
	}
	// 如果设置了最大输出令牌数，则配置到请求中
	if in.MaxOutputToken > 0 {
		chatRequest.MaxTokens = int(in.MaxOutputToken) // 将最大输出令牌数转换为整数并设置
	}
	// 如果需要消耗信用点数，则配置相关参数
	if in.ConsumeCredit {
		chatRequest.ConsumeCredit = &siderllm.ConsumeCreditParam{ // 创建信用消耗参数对象
			NoConsumeMessagesCredit: true, // 不消耗消息信用点
			NoConsumeToolCredit:     true, // 不消耗工具信用点
		}
	}

	// 如果请求消息中包含工具参数设置，则配置到请求中，这块工具应该是后端执行的
	// todo 需要确认
	if in.RequestMessage.ToolsParam != nil {
		chatRequest.ToolsParam = siderllm.ToolsParam{ // 创建工具参数对象
			Auto:  in.RequestMessage.ToolsParam.Auto,  // 设置是否自动使用工具
			Force: in.RequestMessage.ToolsParam.Force, // 设置是否强制使用工具
		}
	}

	//添加历史消息到请求中,包括system prompt和目录
	for i, v := range in.MessageList {

		if i == 0 {
			o.addChatMessage(chatRequest, v.Role, v.MultiContent, v.Token, true) // 第一条消息添加缓存

		} else {
			o.addChatMessage(chatRequest, v.Role, v.MultiContent, v.Token, false)
		}
	}
	//添加父消息
	//添加当前用户的请求消息
	o.addChatMessage(chatRequest, in.RequestMessage.Role, in.RequestMessage.MultiContent, in.RequestMessage.Token, false)
	// o.addChatFunctionMessage(chatRequest, in.RequestMessage.Role, in.RequestMessage.FunctionCall, in.RequestMessage.Token)
	// 返回配置好的聊天请求对象和nil错误
	return chatRequest, nil
}

// 获取聊天历史记录
func (o *ChatWebBiz) getChatMessageList(ctx context.Context, in *ChatUserRequest) (parentMessage, requestMessage *ChatMessage, historyList []*ChatMessage, err error) {
	//历史聊天记录

	if in.ChatHistoryID != "" { // 如果聊天历史ID不为空
		if in.ParentID != "" { // 如果父消息ID不为空
			// 根据父消息ID获取父消息
			parentMessage, err = o.GetMsgById(ctx, in.ChatHistoryID, in.ParentID) // 调用方法获取父消息 这个消息是用户前端展示的上一条消息
			if err != nil {                                                       // 如果获取过程中出现错误
				return // 直接返回，错误信息会通过err变量传递
			}
		}
		if in.NeedHistory && parentMessage != nil { // 如果需要历史数据且用户父消息不为空
			var result []*ChatMessage // 声明一个聊天消息指针数组变量
			// 根据父消息的父ID列表获取历史消息，最多获取11条
			result, err = o.listMessageByIds(ctx, in.ChatHistoryID, parentMessage.ParentIDS, 7) // 调用方法获取历史消息
			if err != nil {                                                                     // 如果获取过程中出现错误
				return // 直接返回，错误信息会通过err变量传递
			}
			result = lo.Reverse(result)                  // 使用lo库的Reverse函数反转消息列表顺序
			if parentMessage.Role == siderllm.RoleUser { // 如果用户父消息的角色是用户 默认认为是重试
				// 如果传入的父级是user message, 则直接使用，不使用前端传入的数据
				requestMessage = parentMessage // 将用户父消息赋值给请求消息
			} else { // 如果用户父消息的角色不是用户
				// 只要最后10条数据
				if len(result) == 7 { // 如果结果长度为11
					result = result[2:] // 截取从索引2开始的所有元素（丢弃前两条消息）
				}
				// 将用户父消息添加到结果列表中
				result = append(result, parentMessage) // 将用户父消息添加到结果列表末尾
			}
			// 设置历史消息列表
			for _, message := range result {
				if message.MultiContent != nil {
					var newMultiContent []documentmodel.MultiContent
					for _, multiContent := range message.MultiContent {
						if multiContent.Type == "file" && multiContent.File != nil && multiContent.File.Type == "image" && multiContent.File.FileID != "" {
							if in.ImageSiderFileID == nil {
								continue
							}
							if _, ok := in.ImageSiderFileID[multiContent.File.FileID]; !ok {
								continue
							}
						}
						newMultiContent = append(newMultiContent, multiContent)

					}
					message.MultiContent = newMultiContent
				}
				historyList = append(historyList, message)
			}
		}
	}
	if requestMessage == nil { //
		// 将请求中的提示转换为文档格式
		prompt := o.requestConvertToDocument(in.Prompt, in.UserID)
		// 创建请求消息对象
		requestMessage = &ChatMessage{
			Role:         siderllm.RoleUser,
			MultiContent: prompt,
			// 复制提示内容到用户多内容字段
			UserMultiContent: lo.Map(prompt, func(item documentmodel.MultiContent, index int) documentmodel.MultiContent {
				return item
			}),
			Hidden:          in.Hidden,
			PromptTemplates: in.PromptTemplates,
			Quote:           in.Quote,
		}
		if in.ToolsParam != nil {
			// 如果请求中包含工具参数，则设置工具参数
			requestMessage.ToolsParam = &documentmodel.ToolsParam{
				Auto:  in.ToolsParam.Auto,
				Force: in.ToolsParam.Force,
			}
		}

		if in.FileIDs != nil {
			requestMessage.LibraryOption = &documentmodel.LibraryOption{Resources: make([]documentmodel.LibraryResource, 0)}
			ids := lo.Map(in.FileIDs, func(item string, index int) primitive.ObjectID {
				id, _ := primitive.ObjectIDFromHex(item)
				return id
			})
			requestMessage.LibraryOption.Resources = append(requestMessage.LibraryOption.Resources, documentmodel.LibraryResource{
				Type: documentmodel.LibraryResourceTypeFile,
				Ids:  ids,
			})
		}
	}
	// 为了处理无分支的情况，每次都必须创建新的消息
	requestMessage.ID = ""

	return
}

// requestConvertToDocument 将API层的MultiContent转换为文档模型的MultiContent
func (o *ChatWebBiz) requestConvertToDocument(contents []*v1.MultiContent, userID int) []documentmodel.MultiContent {
	var normalContents []documentmodel.MultiContent
	for _, content := range contents {
		// 过滤掉不在用户内容类型列表中的内容
		//if !lo.Contains(documentmodel.UserContentTypes, documentmodel.ContentType(content.Type)) {
		//	continue
		//}
		// 去除文本前后的空白字符
		content.ActualText = strings.TrimSpace(content.ActualText)
		content.Text = strings.TrimSpace(content.Text)
		// 创建文档模型的多媒体内容项
		item := documentmodel.MultiContent{
			Type: documentmodel.ContentType(content.Type),
		}
		switch item.Type {
		case documentmodel.ContentTypeText:
			// 处理文本类型内容
			item.Text = content.Text
			if content.ActualText != "" {
				// 如果存在实际文本，则将原始文本保存为用户输入文本
				item.UserInputText = content.Text
				item.Text = content.ActualText
			}
		case documentmodel.ContentTypeFile:
			//处理文件（目前主要指image）
			if content.File.Type == "image" {
				IsSuccess, err := o.ConverImage(&item, content.File.FileID, userID)
				if err != nil {
					slog.Error("ConverImage err:", err)
				}
				if !IsSuccess {
					continue
				}
			}
		}
		// 将处理后的内容添加到结果数组
		normalContents = append(normalContents, item)
	}
	return normalContents
}

func (o *ChatWebBiz) convertPromptTemplates(ctx context.Context, chatReq *ChatRequest) error {
	var auto []string                             // 定义自动工具列表
	if chatReq.RequestMessage.ToolsParam != nil { // 如果工具参数不为空
		auto = chatReq.RequestMessage.ToolsParam.Auto // 获取自动工具列表
	}
	resp, err := o.chatClient.Cli.ConvertPromptTemplate(ctx, &siderllm.PromptTemplateReq{ // 调用客户端转换提示模板
		PromptTemplates: lo.Map(chatReq.RequestMessage.PromptTemplates, func(item documentmodel.PromptTemplate, index int) siderllm.PromptTemplateParam { // 映射提示模板
			// article chat 的业务逻辑，
			return siderllm.PromptTemplateParam{ // 返回提示模板参数
				Key:        item.Key,        // 模板键
				Attributes: item.Attributes, // 模板属性
				Scene:      item.Scene,      // 模板场景
				Platform:   item.Platform,   // 模板平台
			}
		}),
		Tools: auto, // 设置工具列表
		//UserPrompt: userPrompt,            // 设置用户提示
		Model: chatReq.RequestMessage.Model, // 设置模型
	})

	// todo resp是否为nil
	if err != nil { // 如果转换出错
		return err // 返回错误
	}
	if resp == nil { // 如果响应为空
		return nil // 返回空
	}

	var NoCacheSystemPrompt strings.Builder // 使用 strings.Builder 提高性能

	for _, item := range chatReq.RequestMessage.PromptTemplates {
		for key, val := range item.Attributes {
			var text string // 初始化一个局部变量 text
			switch key {
			case "time":
				text = "<CurrentDate>\n\t\t\tCurrent time is: " + val + "\n</CurrentDate>"
			case "files":
				text = "<CurrentFiles>\n\t\t\tThe following are the currently uploaded file contents: " + val + "\n</CurrentFiles>"
			case "code":
				text = "<CurrentCode>\n\t\t\tThe following is the current code content: " + val + "\n</CurrentCode>"
			}
			NoCacheSystemPrompt.WriteString(text) // 使用 WriteString 方法将 text 添加到 NoCacheSystemPrompt
		}
	}

	if len(resp.SystemPrompt) > 0 { // 如果响应中的系统提示不为空
		chatReq.MessageList = pie.Insert(chatReq.MessageList, 0, &ChatMessage{ // 在消息列表开头插入非缓存消息，
			Role: siderllm.RoleSystem, // 设置角色为系统
			MultiContent: []documentmodel.MultiContent{ // 设置多内容
				{
					Type: ChatMessageTypeText,          // 设置类型为文本
					Text: NoCacheSystemPrompt.String(), // 设置文本内容为响应的系统提示
				},
			},
		})
		chatReq.MessageList = pie.Insert(chatReq.MessageList, 0, &ChatMessage{ //在消息列表开头插入系统消息
			Role: siderllm.RoleSystem, // 设置角色为系统
			MultiContent: []documentmodel.MultiContent{ // 设置多内容
				{
					Type: ChatMessageTypeText, // 设置类型为文本
					Text: resp.SystemPrompt,   // 设置文本内容为响应的系统提示
				},
			},
		})

	}

	return nil // 返回成功
}

func (o *ChatWebBiz) GetMsgById(ctx context.Context, historyId, msgId string) (*ChatMessage, error) {
	// 调用仓库层方法根据历史ID和消息ID获取消息
	msg, err := o.repo.GetMsgById(ctx, historyId, msgId)
	// 如果获取过程中发生错误，则返回错误
	if err != nil {
		return nil, err
	}
	// 将数据库消息对象转换为业务层消息对象并返回
	return o.convertMsg(msg), nil
}

func (o *ChatWebBiz) convertMsg(msg *documentmodel.ChatMessage) *ChatMessage {
	// 初始化父消息ID变量
	var parentID string
	// 如果消息的父ID不为空，则转换为十六进制字符串
	if !msg.ParentID.IsZero() {
		parentID = msg.ParentID.Hex()
	}
	// 返回转换后的ChatMessage对象
	return &ChatMessage{
		ID:                 msg.ID.Hex(),                 // 将ObjectID转换为十六进制字符串
		Model:              msg.Model,                    // 复制模型信息
		Hidden:             msg.Hidden,                   // 复制隐藏状态
		MultiContent:       msg.MultiContent,             // 复制多内容数据
		Role:               msg.Role,                     // 复制角色信息
		Level:              msg.Level,                    // 复制消息层级
		ChatConversationID: msg.ChatConversationID.Hex(), // 将会话ID转换为十六进制字符串
		ParentIDS: lo.Map(msg.ParentIDS, func(item primitive.ObjectID, index int) string {
			return item.Hex() // 将所有父消息ID转换为十六进制字符串
		}),
		ToolsParam:      msg.ToolsParam,      // 复制工具参数
		ResponseFormat:  msg.ResponseFormat,  // 复制响应格式
		LibraryOption:   msg.LibraryOption,   // 复制库选项
		PromptTemplates: msg.PromptTemplates, // 复制提示模板
		AsyncTask:       msg.AsyncTask,       // 复制异步任务信息
		ParentID:        parentID,            // 设置处理后的父消息ID
		CurrentChildID:  msg.CurrentChildID,  // 复制当前子ID
		Status:          msg.Status,          // 复制状态信息
		UserQuotaInfo:   msg.UserQuotaInfo,   // 复制用户配额信息
		AppName:         msg.AppName,         // 复制应用名称
		AppVersion:      msg.AppVersion,      // 复制应用版本
		TraceID:         msg.TraceID,         // 复制跟踪ID
		TimeZone:        msg.TimeZone,        // 复制时区信息
		SubMessages:     msg.SubMessages,     // 复制子消息列表
	}
}

func (o *ChatWebBiz) listMessageByIds(ctx context.Context, historyId string, parentIds []string, limit int64) ([]*ChatMessage, error) {
	messages, err := o.repo.ListMsgByIds(ctx, historyId, parentIds, limit)
	if err != nil {
		return nil, err
	}

	return lo.Map(messages, func(msg *documentmodel.ChatMessage, index int) *ChatMessage {
		return o.convertMsg(msg)
	}), nil
}

// createChatMessage 创建聊天消息并保存到数据库

func (o *ChatWebBiz) createChatMessage(ctx context.Context, in *ChatRequest) error {
	// 从上下文中获取调用信息，包括应用名称、版本、跟踪ID和时区
	callInfo := ginutil.GetCallInfo(ctx)
	var appName string
	var appVersion string
	var traceID string
	var timeZone string
	if callInfo != nil {
		appName = callInfo.AppName
		appVersion = callInfo.AppVersion
		traceID = callInfo.TraceID
		timeZone = callInfo.TimeZone
	}

	// 如果Save为true，则保存聊天记录到数据库
	if in.Save {
		var err error
		// 如果用户消息存在且ID为空（新消息），则保存用户消息
		if in.RequestMessage != nil && in.RequestMessage.ID == "" {
			in.RequestMessage, err = o.saveMessage(ctx, in.ParentMessage, &ChatMessage{
				Role:               in.RequestMessage.Role,             // 消息角色（用户）
				MultiContent:       in.RequestMessage.MultiContent,     // 消息内容
				UserMultiContent:   in.RequestMessage.UserMultiContent, // 用户原始输入内容
				Hidden:             in.RequestMessage.Hidden,           // 是否隐藏消息
				ChatConversationID: in.ChatHistoryID,                   // 会话ID
				Model:              in.Model,                           // 使用的模型
				ActualModel:        in.GetActualModel(),                // 实际使用的模型
				Status:             documentmodel.MessageStatusSuccess, // 消息状态为成功
				AppName:            appName,                            // 应用名称
				AppVersion:         appVersion,                         // 应用版本
				TraceID:            traceID,                            // 跟踪ID，用于日志追踪
				TimeZone:           timeZone,                           // 用户时区
				PromptTemplates:    in.RequestMessage.PromptTemplates,  // 提示模板
				Quote:              in.RequestMessage.Quote,            // 引用内容
				ResponseFormat:     in.RequestMessage.ResponseFormat,   // 响应格式
				ToolsParam:         in.RequestMessage.ToolsParam,       // 工具参数
				LibraryOption:      in.RequestMessage.LibraryOption,    // 库选项
			}, in.RequestMessage.UserMultiContent)
			if err != nil {
				return api.ErrorCreateOneFailed("Save User Message Failed. ")
			}
		}

		// 如果存在父消息，更新父消息的子消息ID和状态
		if in.ParentMessage != nil {
			var childId primitive.ObjectID
			if in.ParentMessage.CurrentChildID.IsZero() {
				childId, _ = primitive.ObjectIDFromHex(in.RequestMessage.ID)
			}
			var status documentmodel.MessageStatus
			// 如果父消息状态为已创建且没有异步任务，则将状态设为错误
			if in.ParentMessage.Status == documentmodel.MessageStatusCreated && in.ParentMessage.AsyncTask == nil {
				status = documentmodel.MessageStatusError
			}
			if err = o.repo.UpdateChildrenID(ctx, in.ChatHistoryID, in.ParentMessage.ID, childId, status); err != nil {
				slog.ErrorContext(ctx, "update parent child id and status failed ", err)
			}
		}

		// 创建并保存AI回复消息
		in.AIMessage, err = o.saveMessage(ctx, in.RequestMessage, &ChatMessage{
			Model:              in.Model,                           // 使用的模型
			ChatConversationID: in.ChatHistoryID,                   // 会话ID
			Status:             documentmodel.MessageStatusCreated, // 消息状态为已创建
			Role:               siderllm.RoleAssistant,             // 消息角色（助手）
			Hidden:             false,                              // 不隐藏消息
			AppName:            appName,                            // 应用名称
			AppVersion:         appVersion,                         // 应用版本
			TraceID:            traceID,                            // 跟踪ID
			TimeZone:           timeZone,                           // 时区
		}, nil)
		if err != nil {
			return api.ErrorCreateOneFailed("Save AI Message Failed. ")
		}

		// 更新用户消息的子消息ID为AI消息ID
		var childId primitive.ObjectID
		childId, _ = primitive.ObjectIDFromHex(in.AIMessage.ID)
		if err = o.repo.UpdateChildrenID(ctx, in.ChatHistoryID, in.RequestMessage.ID, childId, ""); err != nil {
			slog.ErrorContext(ctx, "update parent child id and status failed ", err)
		}
	} else {
		// 不保存该条记录，仅创建内存中的AI消息对象
		in.AIMessage = &ChatMessage{
			ID:                 "",                     // 空ID表示不保存
			Model:              in.Model,               // 使用的模型
			Hidden:             false,                  // 不隐藏消息
			MultiContent:       nil,                    // 初始内容为空
			Role:               siderllm.RoleAssistant, // 消息角色（助手）
			ChatConversationID: "",                     // 空会话ID
		}
	}

	// 如果需要保存且会话ID不为空，更新会话的叶子节点和消息关系
	if in.Save && in.ChatHistoryID != "" {
		saveData := ChatConversationUpdate{
			LeafMessageID: in.AIMessage.ID, // 设置新的叶子消息ID为AI消息ID
			MessagesCurrentChildrenID: map[string]string{
				in.RequestMessage.ID: in.AIMessage.ID, // 用户消息的当前子消息为AI消息
			},
		}
		// 如果用户消息有父消息，更新父消息的当前子消息为用户消息
		if in.RequestMessage.ParentID != "" {
			saveData.MessagesCurrentChildrenID[in.RequestMessage.ParentID] = in.RequestMessage.ID
		}
		// 在conversation表中更新会话信息
		if saveErr := o.repo.UpdateChatConversation(ctx, in.ChatHistoryID, saveData); saveErr != nil {
			return api.ErrorUpdateOneFailed(saveErr.Error())
		}
	}
	return nil
}

// 保存聊天消息到数据库
// 目前不确定为什么ChatMessage中已经有了multiContent还要再单独传呢？
func (o *ChatWebBiz) saveMessage(ctx context.Context, parentChatMessage, g *ChatMessage, multiContent []documentmodel.MultiContent) (*ChatMessage, error) {
	// 初始化父消息ID相关变量
	var parentIds []primitive.ObjectID // 所有祖先消息ID列表
	var parentID primitive.ObjectID    // 直接父消息ID
	level := 1                         // 消息层级，默认为1（顶层消息）

	// 如果存在父消息，设置父消息相关信息
	if parentChatMessage != nil {
		// 将父消息ID转换为ObjectID类型
		parentID, _ = primitive.ObjectIDFromHex(parentChatMessage.ID)
		// 构建完整的祖先消息ID链，包括父消息自身的ID
		parentIds = lo.Map(append(parentChatMessage.ParentIDS, parentChatMessage.ID), func(item string, index int) primitive.ObjectID {
			id, _ := primitive.ObjectIDFromHex(item)
			return id
		})
		// 消息层级为父消息层级+1
		level = parentChatMessage.Level + 1
	}

	// 将会话ID字符串转换为ObjectID类型
	cid, _ := primitive.ObjectIDFromHex(g.ChatConversationID)

	// 创建数据库消息对象
	msg := &documentmodel.ChatMessage{
		MultiContent:       multiContent,      // 消息的多媒体内容
		Model:              g.Model,           // 使用的模型名称
		ActualModel:        g.ActualModel,     // 实际使用的模型名称
		Hidden:             g.Hidden,          // 是否隐藏消息
		Role:               g.Role,            // 消息角色（用户/助手）
		ParentID:           parentID,          // 直接父消息ID
		ParentIDS:          parentIds,         // 所有祖先消息ID列表
		ChatConversationID: cid,               // 会话ID
		Level:              level,             // 消息层级
		AppName:            g.AppName,         // 应用名称
		AppVersion:         g.AppVersion,      // 应用版本
		TraceID:            g.TraceID,         // 跟踪ID，用于日志追踪
		TimeZone:           g.TimeZone,        // 用户时区
		Status:             g.Status,          // 消息状态
		PromptTemplates:    g.PromptTemplates, // 提示模板
		Quote:              g.Quote,           // 引用内容
		ToolsParam:         g.ToolsParam,      // 工具参数
		ResponseFormat:     g.ResponseFormat,  // 响应格式
		LibraryOption:      g.LibraryOption,   // 库选项
		UserQuotaInfo:      g.UserQuotaInfo,   // 用户积分信息
	}

	// 将消息保存到数据库并获取生成的ID
	id, err := o.repo.CreateMessage(ctx, msg)
	if err != nil {
		return nil, err
	}

	// 将数据库消息对象转换为业务层消息对象
	result := o.convertMsg(msg)
	result.ID = id                               // 设置创建消息时生成的ID
	result.MultiContent = g.MultiContent         // 保留原始多媒体内容
	result.UserMultiContent = g.UserMultiContent // 保留用户原始输入内容
	return result, nil
}

func (o *ChatWebBiz) UpdateCreditUsage(ctx context.Context, ChatConversationID string, g *ChatMessage) error {
	conversationId, _ := primitive.ObjectIDFromHex(g.ChatConversationID)
	userQuotaInfo := &documentmodel.UserQuotaInfo{
		CreditType: g.CreditType,
		Count:      g.CreditCount,
	}
	if g.IsTrialCredit {
		userQuotaInfo.Feature = userquota.FeatureTypeWebCodeFreeTrial

	}
	return o.repo.UpdateMessageCreditUsage(ctx, ChatConversationID, &documentmodel.ChatMessage{
		ChatConversationID: conversationId,
		UserQuotaInfo:      userQuotaInfo,
	})
}

func (o *ChatWebBiz) transformCodePlaceholdersList(fullContent []siderllm.FullContent) error {
	for i, content := range fullContent {
		if content.Type == siderllm.ContentTypeText {
			newText, err := o.transformCodePlaceholders(content.Text)
			if err != nil {
				return err
			}
			fullContent[i].Text = newText
		}
	}
	return nil
}

func (o *ChatWebBiz) transformCodePlaceholders(input string) (string, error) {

	// 通用正则表达式，使用 (?s) 选项以允许点匹配换行
	re, err := regexp.Compile(`(?s)<code-write>\s*<code-path>(.*?)</code-path>\s*<code-content>(.*?)</code-content>\s*</code-write>`)
	if err != nil {
		return "", err // 返回空字符串表示出错
	}

	// 改用 FindAllStringSubmatch 进行调试
	//matches := re.FindStringSubmatch(input)

	// 替换匹配的字符串
	result := re.ReplaceAllString(input, `<code-write>
     <code-path>$1</code-path>
     <code-content><The code content has been applied to the code environment></code-content>
   </code-write>`)

	return result, nil
}

// ConverImage
func (o *ChatWebBiz) ConverImage(item *documentmodel.MultiContent, siderFileID string, userID int) (bool, error) {
	thumbnailKey, err := o.GetThumbnailTask(siderFileID, userID)
	if err != nil {
		return false, err
	}
	if thumbnailKey == "" {
		return false, nil
	}
	item.File = &documentmodel.ChatFile{
		Type:      documentmodel.ChatFileTypeImage,
		ObjectKey: thumbnailKey,
		FileID:    siderFileID,
	}
	return true, nil
}

func (o *ChatWebBiz) GetThumbnailTask(siderFileID string, userID int) (string, error) {
	resp, err := o.siderFileClient.GetFile(siderFileID).RequestFromInnerWithUserID(context.Background(), userID)
	if err != nil {
		return "", err
	}
	if !resp.Success() {
		return "", nil
	}
	if resp.Data.ThumbnailTask != nil && resp.Data.ThumbnailTask.GetStatus().IsCompleted() {
		return resp.Data.ThumbnailTask.Result.Key, nil
	}
	return "", nil
}
