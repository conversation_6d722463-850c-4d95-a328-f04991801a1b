package quota

import (
	"context"
	"fmt"
	"log/slog"
	"strconv"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"

	"web-coder/internal/third_client"

	"github.com/go-kratos/kratos/v2/errors"

	"github.com/Sider-ai/go-pkg/ginutil"
	"github.com/Sider-ai/go-pkg/userquota"
	sidererrors "github.com/Sider-ai/sider-errors"

	"github.com/Sider-ai/go-pkg/clientinfo"
	"github.com/Sider-ai/go-pkg/commondto"
	"github.com/oklog/ulid/v2"

	"web-coder/api"
)

// 定义操作的额度配置
type QuotaConfig struct {
	CreditType  userquota.CreditType
	CreditCount int
}

type ModelCostsConfig struct {
	inputTokenCosts         float64
	outputTokenCosts        float64
	cacheTokenCosts         float64
	cacheCreationTokenCosts float64
	creditType              userquota.CreditType // 积分类型：基础积分或高级积分
}

// 额度管理器
type UserQuotaManager struct {
	client *UserQuotaClient // 用于与配额服务通信的客户端，封装了go-pkg中的配额客户端
	mu     sync.RWMutex     // 用于保护并发访问的读写锁
	// 具体的操作类型，是直接在 proto 文件里定义了，业务代码里直接引用，不需要再定义和在业务代码里参数校验
	statisQuotaMap map[api.QuotaOperation]QuotaConfig // 存储不同操作类型对应的配额配置，包括额度类型和数量
	modelCostsMap  map[string]ModelCostsConfig        // 存储不同模型的成本配置，包括输入、输出和缓存的token成本
	log            *slog.Logger                       // 用于记录日志的logger实例
	redis          *third_client.RedisClient          // Redis客户端，用于缓存和分布式锁等功能
	tokenClient    *third_client.TokenClient
}

func NewQuotaManager(
	client *UserQuotaClient,
	logger *slog.Logger,
	r *third_client.RedisClient,
	tokenClient *third_client.TokenClient,
) *UserQuotaManager {
	qm := &UserQuotaManager{
		client:      client,
		log:         logger,
		tokenClient: tokenClient,
		modelCostsMap: map[string]ModelCostsConfig{
			"gpt-4o":                  {inputTokenCosts: 0.0000025, outputTokenCosts: 0.00001, cacheTokenCosts: 0.00000125, cacheCreationTokenCosts: 0.0, creditType: userquota.CreditTypeBasic},
			"gpt-4o-mini":             {inputTokenCosts: 0.00000015, outputTokenCosts: 0.0000006, cacheTokenCosts: 0.000000075, cacheCreationTokenCosts: 0.0, creditType: userquota.CreditTypeBasic},
			"o3-mini":                 {inputTokenCosts: 0.0000011, outputTokenCosts: 0.0000044, cacheTokenCosts: 0.00000055, cacheCreationTokenCosts: 0.0, creditType: userquota.CreditTypeAdvanced},
			"o3":                      {inputTokenCosts: 0.000002, outputTokenCosts: 0.000008, cacheTokenCosts: 0.0000005, cacheCreationTokenCosts: 0.0, creditType: userquota.CreditTypeAdvanced},
			"gpt-4.1":                 {inputTokenCosts: 0.000002, outputTokenCosts: 0.000008, cacheTokenCosts: 0.0000005, cacheCreationTokenCosts: 0.0, creditType: userquota.CreditTypeAdvanced},
			"gpt-4.1-mini":            {inputTokenCosts: 0.0000004, outputTokenCosts: 0.0000016, cacheTokenCosts: 0.0000001, cacheCreationTokenCosts: 0.0, creditType: userquota.CreditTypeBasic},
			"claude-3.7-sonnet":       {inputTokenCosts: 0.000003, outputTokenCosts: 0.000015, cacheTokenCosts: 0.0000003, cacheCreationTokenCosts: 0.00000375, creditType: userquota.CreditTypeAdvanced},
			"claude-3.7-sonnet-think": {inputTokenCosts: 0.000003, outputTokenCosts: 0.000015, cacheTokenCosts: 0.0000003, cacheCreationTokenCosts: 0.00000375, creditType: userquota.CreditTypeAdvanced},
			"claude-4-sonnet":         {inputTokenCosts: 0.000003, outputTokenCosts: 0.000015, cacheTokenCosts: 0.0000003, cacheCreationTokenCosts: 0.00000375, creditType: userquota.CreditTypeAdvanced},
			"claude-4-sonnet-think":   {inputTokenCosts: 0.000003, outputTokenCosts: 0.000015, cacheTokenCosts: 0.0000003, cacheCreationTokenCosts: 0.00000375, creditType: userquota.CreditTypeAdvanced},
			"gemini-2.5-pro":          {inputTokenCosts: 0.00000125, outputTokenCosts: 0.00001, cacheTokenCosts: 0.0, cacheCreationTokenCosts: 0.0, creditType: userquota.CreditTypeAdvanced}, //缓存价格需重新计算
			"qwen3-coder-plus":        {inputTokenCosts: 0.000001, outputTokenCosts: 0.000004, cacheTokenCosts: 0.0, cacheCreationTokenCosts: 0.0, creditType: userquota.CreditTypeBasic},
			"kimi-k2":                 {inputTokenCosts: 0.00000055, outputTokenCosts: 0.00000222, cacheTokenCosts: 0.00000014, cacheCreationTokenCosts: 0.0, creditType: userquota.CreditTypeBasic},
			"glm-4.5":                 {inputTokenCosts: 0.00000015, outputTokenCosts: 0.00000035, cacheTokenCosts: 0.0, cacheCreationTokenCosts: 0.0, creditType: userquota.CreditTypeBasic},
		},
		// statisQuotaMap 存储各种操作对应的额度配置
		// 键为几分类型如：basic和advanced，值为对应的额度类型和数量
		statisQuotaMap: map[api.QuotaOperation]QuotaConfig{
			api.QuotaOperation_EDITOR_GENERATE_IMAGE: {CreditType: userquota.CreditTypeAdvanced, CreditCount: 1},
		},
		redis: r,
	}
	return qm
}

// 获取用户额度信息

func (qm *UserQuotaManager) GetUserQuotaInfo(ctx context.Context, userId int) (*userquota.QuotaInfoResult, error) {
	Info := ginutil.GetCallInfo(ctx)
	param := &userquota.QuotaInfoParam{
		ClientInfo: clientinfo.ClientInfo{
			AppName:    Info.AppName,
			AppVersion: Info.AppVersion,
		},
		TimeZone: commondto.TimeZone{
			TZName: Info.TimeZone,
		},
		UserID: userId,
	}
	return qm.client.Cli.GetUserQuotaInfo(ctx, param)
}

// GetModelCreditType 获取模型对应的积分类型
func (qm *UserQuotaManager) GetModelCreditType(model string) userquota.CreditType {
	if config, exists := qm.modelCostsMap[model]; exists {
		return config.creditType
	}
	// 默认返回高级积分
	return userquota.CreditTypeAdvanced
}

// IsValidModel 检查模型是否在支持的模型列表中
func (qm *UserQuotaManager) IsValidModel(model string) bool {
	_, exists := qm.modelCostsMap[model]
	return exists
}

// modelCoefficient 模型系数，用于调整不同模型的额度消耗比例
// 默认值为1.0，表示标准消耗比例
const (
	modelCoefficient = 1
	// basicCreditCosts 基础额度成本，表示每个基础额度的标准成本
	// 当累积的token成本达到此值时，将消耗一个基础额度
	basicCreditCosts   = 0.0016
	advanceCreditCosts = 0.0135
)

// GetUserAccmuCreditCount 从Redis获取用户累计的积分数量
func (qm *UserQuotaManager) GetUserAccmuCreditCount(ctx context.Context, redisKey string) (float64, error) {
	accumulatedToken := 0.0
	accumulatedTokenStr, err := qm.redis.Client.Get(ctx, redisKey).Result()
	if err != nil {
		// 区分键不存在和其他错误
		if errors.Is(err, redis.Nil) {
			// 键不存在属于正常情况，返回默认值0.0
			return accumulatedToken, nil
		}
		return accumulatedToken, err
	}
	if accumulatedTokenStr != "" {
		accumulatedToken, _ = strconv.ParseFloat(accumulatedTokenStr, 64)
	}
	return accumulatedToken, nil
}

func (qm *UserQuotaManager) SetUserAccumulatedCredit(ctx context.Context, redisKey string, value float64) error {
	err := qm.redis.Client.Set(ctx, redisKey, fmt.Sprintf("%f", value), 7*24*time.Hour).Err()
	if err != nil {
		return err
	}
	return nil
}

type TokenUseage struct {
	CreditCount      float64
	InputTokenCosts  float64
	InputTokens      int
	OutputTokenCosts float64
	OutputTokens     int
	CacheTokenCosts  float64
	CacheTokens      int
	ModelCoefficient float64
	BasicCreditCosts float64
	AccumulatedToken float64
	IntegerPart      int
	DecimalPart      float64
}

func (qm *UserQuotaManager) DeductTrialQuotaChat(ctx context.Context,
	userId int) *errors.Error {
	// 2. 将 creditCount 整数部分扣费
	clientInfo := ginutil.GetCallInfo(ctx)
	param := &userquota.DeductionQuotaParam{
		ClientInfo: clientinfo.ClientInfo{
			AppName:    clientInfo.AppName,
			AppVersion: clientInfo.AppVersion,
		},
		TimeZone: commondto.TimeZone{
			TZName: clientInfo.TimeZone,
		},
		UserID:        userId,
		TraceID:       clientInfo.TraceID,
		TransactionID: ulid.Make().String(),
		CreditType:    userquota.CreditTypeAdvanced,
		CreditCount:   0,
		Feature:       userquota.FeatureTypeWebCodeFreeTrial,
	}

	_, err := qm.client.Cli.DeductionQuota(ctx, param)
	if err != nil {
		slog.ErrorContext(ctx, err.Error())
		apiErr := qm.getError(err)
		return api.ErrorDeductQuota(fmt.Sprintf("Deduction Inter part error: %s, the Feature  is %d", apiErr.Error(), userquota.FeatureTypeWebCodeFreeTrial))
	}
	return nil

}

func (qm *UserQuotaManager) DeductQuotaChat(
	ctx context.Context,
	userId int,
	model string,
	inputTokens int,
	outputTokens int,
	cachedTokens int,
	CacheCreationTokens int,
	remainCredit int,
) (*TokenUseage, *errors.Error) {
	// qwen的缓存是无效的
	if model == "qwen3-coder-plus" {
		cachedTokens = 0
	}
	// 1. 计算扣费
	// cachetoken 属于 inputtoken 的一部分
	inputTokens = inputTokens - cachedTokens

	creditType := qm.GetModelCreditType(model)

	// 根据模型的成本配置计算各部分token的成本
	inputTokenCosts := qm.modelCostsMap[model].inputTokenCosts * float64(inputTokens)
	outputTokenCosts := qm.modelCostsMap[model].outputTokenCosts * float64(outputTokens)
	cacheTokenCosts := qm.modelCostsMap[model].cacheTokenCosts * float64(cachedTokens)
	totalTokenCosts := inputTokenCosts + outputTokenCosts + cacheTokenCosts
	var cacheCreationTokenCosts float64
	if CacheCreationTokens != 0 {
		cacheCreationTokenCosts = qm.modelCostsMap[model].cacheCreationTokenCosts * float64(CacheCreationTokens)
		totalTokenCosts += cacheCreationTokenCosts
	}

	// 根据积分类型选择对应的Redis键和成本计算
	var redisKey string
	var creditCosts float64
	if creditType == userquota.CreditTypeBasic {
		redisKey = fmt.Sprintf("user:%d:basicAccumulatedToken", userId)
		creditCosts = basicCreditCosts
	} else {
		redisKey = fmt.Sprintf("user:%d:advancedAccumulatedToken", userId)
		creditCosts = advanceCreditCosts
	}

	// 从Redis获取用户上次计算的小数部分积分
	quotaCtx := context.Background()
	accumulatedToken, err := qm.GetUserAccmuCreditCount(quotaCtx, redisKey)
	if err != nil {
		return nil, api.ErrorDeductQuota(fmt.Sprintf("error get GetUserAccmuCreditCount from redis %s\n", err.Error()))
	}

	// 计算本次需要扣除的总积分(包含上次的小数部分)
	creditCount := (totalTokenCosts * modelCoefficient / creditCosts) + accumulatedToken
	integerPart := int(creditCount)                   // 取整数部分
	decimalPart := creditCount - float64(integerPart) // 取小数部分

	if integerPart == 0 {
		integerPart = 1
		decimalPart = float64(0)
	}

	// 如果按 0 额度扣，扣费sdk那边默认扣1，所以为 0 就不扣
	slog.Info("remainCredit:", remainCredit, userId)

	userIntegerPart := integerPart
	// 不够扣的话, 扣用户的剩余积分即可
	if remainCredit < integerPart {
		integerPart = remainCredit
	}

	callInfo := ginutil.GetCallInfo(ctx)
	var traceID string
	if callInfo != nil {
		traceID = callInfo.TraceID
	}
	slog.Info("totalTokenCosts:", map[string]interface{}{
		"traceID":                 traceID,
		"model":                   model,
		"creditType":              creditType,              //积分类型
		"inputTokens":             inputTokens,             //输入token
		"outputTokens":            outputTokens,            //输出token
		"cachedTokens":            cachedTokens,            //缓存token
		"CacheCreationTokens":     CacheCreationTokens,     //创建缓存token
		"inputTokenCosts":         inputTokenCosts,         //输入token费用
		"outputTokenCosts":        outputTokenCosts,        //输出token费用
		"cacheTokenCosts":         cacheTokenCosts,         //缓存token费用
		"cacheCreationTokenCosts": cacheCreationTokenCosts, //创建缓存token的费用
		"totalTokenCosts":         totalTokenCosts,         //当前总费用（不含redis）
		"accumulatedToken":        accumulatedToken,        //redis中累计的积分小数点
		"remainCredit":            remainCredit,            //用户余额
		"creditCount":             creditCount,             //计算的总计分
		"integerPart":             integerPart,             //返回给前端的积分
		"userId":                  userId,
	})

	// 2. 将 creditCount 整数部分扣费
	clientInfo := ginutil.GetCallInfo(ctx)
	param := &userquota.DeductionQuotaParam{
		ClientInfo: clientinfo.ClientInfo{
			AppName:    clientInfo.AppName,
			AppVersion: clientInfo.AppVersion,
		},
		TimeZone: commondto.TimeZone{
			TZName: clientInfo.TimeZone,
		},
		UserID:        userId,
		TraceID:       clientInfo.TraceID,
		TransactionID: ulid.Make().String(),
		CreditType:    creditType,  // 使用模型对应的积分类型
		CreditCount:   integerPart, // 使用计算出的整数部分积分扣费
	}

	// 调用配额服务进行实际扣费
	_, err = qm.client.Cli.DeductionQuota(quotaCtx, param)
	if err != nil {
		slog.ErrorContext(ctx, err.Error())
		apiErr := qm.getError(err)
		return nil, api.ErrorDeductQuota(fmt.Sprintf("Deduction Inter part error: %s, the integerPart part is %d", apiErr.Error(), integerPart))
	}

	// 保存小数部分到 Redis 累计积分, 7 天过期
	err = qm.SetUserAccumulatedCredit(quotaCtx, redisKey, decimalPart)
	if err != nil {
		return nil, api.ErrorDeductQuota(fmt.Sprintf("error get SetUserAccmuCreditCount from redis %s\n", err.Error()))
	}

	// 返回完整的token使用情况和额度计算详情
	return &TokenUseage{
		CreditCount:      creditCount,
		InputTokenCosts:  qm.modelCostsMap[model].inputTokenCosts,
		InputTokens:      inputTokens,
		OutputTokenCosts: qm.modelCostsMap[model].outputTokenCosts,
		OutputTokens:     outputTokens,
		CacheTokenCosts:  qm.modelCostsMap[model].cacheTokenCosts,
		CacheTokens:      cachedTokens,
		ModelCoefficient: modelCoefficient,
		BasicCreditCosts: creditCosts,
		AccumulatedToken: accumulatedToken,
		IntegerPart:      userIntegerPart,
		DecimalPart:      decimalPart,
	}, nil
}

func (qm *UserQuotaManager) getError(err error) *errors.Error {
	if sidererrors.BasicCreditInsufficient.Is(err) {
		return api.ErrorBasicUserQuotaInsufficient(err.Error())
	}
	if sidererrors.AdvancedCreditInsufficient.Is(err) {
		return api.ErrorAdvanceUserQuotaInsufficient(err.Error())
	}
	if sidererrors.RateLimitExceeded.Is(err) {
		return api.ErrorQuotaRateLimitExceeded(err.Error())
	}
	if sidererrors.AdvancedRateLimit.Is(err) {
		return api.ErrorQuotaAdvancedRateLimit(err.Error())
	}
	if sidererrors.BasicRateLimit.Is(err) {
		return api.ErrorQuotaBasicRateLimit(err.Error())
	}

	return api.ErrorDeductQuota(err.Error())
}

func (qm *UserQuotaManager) TikToken(inputText, outputText, model string) (inputToken, outputToken int) {
	inputToken = qm.tokenClient.TikToken(model, inputText)
	outputToken = qm.tokenClient.TikToken(model, outputText)
	return inputToken, outputToken
}
