package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"strings"
	"time"
	"web-coder/api"
	v1 "web-coder/api/chat_web/v1"
	"web-coder/internal/biz"
	"web-coder/internal/biz/quota"
	"web-coder/internal/third_client"

	"github.com/Sider-ai/go-pkg/ginutil"
	"github.com/samber/lo"

	"github.com/Sider-ai/go-pkg/documentmodel"
	"github.com/Sider-ai/go-pkg/siderllm"
	"github.com/Sider-ai/go-pkg/userquota"
	"github.com/go-kratos/kratos/v2/errors"
)

type ChatWebService struct {
	chatWebBiz     *biz.ChatWebBiz
	chatHistoryBiz *biz.ChatHistoryUsecase
	toolBiz        *biz.ChatToolBiz
	chatFileBiz    *biz.ChatFileBiz
	userQM         *quota.UserQuotaManager
	wisebaseClient third_client.WisebaseClient
}

func NewChatWebService(
	chatWebBiz *biz.ChatWebBiz,
	chatHistoryBiz *biz.ChatHistoryUsecase,
	toolBiz *biz.ChatToolBiz,
	chatFileBiz *biz.ChatFileBiz,
	userQuotaManger *quota.UserQuotaManager,
	wisebaseClient third_client.WisebaseClient,
) *ChatWebService {
	return &ChatWebService{
		userQM:         userQuotaManger,
		chatWebBiz:     chatWebBiz,
		chatHistoryBiz: chatHistoryBiz,
		toolBiz:        toolBiz,
		chatFileBiz:    chatFileBiz,
		wisebaseClient: wisebaseClient,
	}
}

const (
	MaxOutputToken = 128 * 1024 // 128K
	MaxInputToken  = 128 * 1024
)

func (s *ChatWebService) Chat(ctx context.Context, notifyCh chan []byte, in *v1.WebChatRequest) error {
	s.chat(ctx, in, notifyCh, func() *errors.Error {
		if in.Chat.ToolsParam != nil && in.Chat.ToolsParam.Auto != nil {
			var newAuto []string
			for _, tool := range in.Chat.ToolsParam.Auto {
				if tool != "search" {
					newAuto = append(newAuto, tool)
				}
			}
			in.Chat.ToolsParam.Auto = newAuto
		}
		// 如果不是指定的两个模型，默认使用 GPT5
		if in.Chat.Model != biz.ModelNameGLM4Dot5 && in.Chat.Model != biz.ModelNameGPT5Think {
			in.Chat.Model = biz.ModelNameGPT5Think
		}

		//从上下文中获取当前用户信息
		userInfo := ginutil.GetCurrentUserInfo(ctx)
		if userInfo == nil {
			return api.ErrorGetOneFailed("Failed to retrieve user information from context.")
		}
		userID, _, _ := s.getUserId(ctx)

		if in.WisebaseID == "" {
			return api.ErrorGetOneFailed("Failed to create chat session: missing wisebase ID")
		}

		//if !s.userQM.IsValidModel(in.Chat.Model) {
		//	return api.ErrorGetOneFailed(fmt.Sprintf("Unsupported model: %s", in.Chat.Model))
		//}

		// 构建用户请求对象
		userReq, reqErr := s.getChatUserRequest(ctx, in.Chat, userID, in.WisebaseID)
		if reqErr != nil {
			return v1.ErrorChatRequestParamsInvalid("%v", reqErr)
		}
		userReq.NeedHistory = true //当有historyID和parentID 都存在则会将历史聊天记录携带上给模型
		if in.Chat.File != nil {
			for _, file := range in.Chat.File {
				if file.Type == "file" {
					userReq.FileIDs = append(userReq.FileIDs, file.LibraryFileID)
				}
			}
		}

		for _, item := range in.Chat.Prompt {
			if item.Type != "file" {
				continue
			}
			if item.File.Type == "image" {
				userReq.FileIDs = append(userReq.FileIDs, item.File.LibraryFileID)
			}
		}

		// 获取聊天请求对象
		chatReq, chatReqErr := s.chatToRequest(ctx, userReq)
		if chatReqErr != nil {
			return v1.ErrorChatRequestParamsInvalid("%v", chatReqErr)
		}
		// 设置聊天请求的各种参数
		chatReq.Save = true                     // 是否保存历史 todo 目前聊天历史记录是必须保存的，后续有需求再加
		chatReq.HiddenModelReturn = true        // 隐藏模型返回
		chatReq.MaxRequestToken = MaxInputToken // 最大请求token数
		chatReq.MaxToolCallCount = 100          // 最大工具调用次数
		chatReq.ConsumeCredit = true            // 消耗积分
		chatReq.MaxOutputToken = MaxOutputToken // 最大输出token数
		// chatReq.Model = biz.ModelNameClaude4Thinking  使用前端传递的模型，已在chatToRequest中设置

		// GLM4.5免费和gpt5限时免费
		if chatReq.Model == biz.ModelNameGLM4Dot5 || chatReq.Model == biz.ModelNameGPT5Think {
			return s.chatFree(ctx, in, chatReq, userInfo, notifyCh)
		}

		// 查询用户积分余额
		// 先查询积分余额，然后进行聊天，最后扣除积分
		// 注意：可能存在并发问题，即查询时有积分，但在聊天过程中被其他请求消耗
		userQuotaInfo, quotaInfoErr := s.userQM.GetUserQuotaInfo(ctx, int(userID))
		if quotaInfoErr != nil {
			return api.ErrorDeductQuota(fmt.Sprintf("Error GetUserQuotaInfo Insufficient %s", quotaInfoErr.Error()))
		}

		// 获取模型对应的积分类型
		modelCreditType := s.userQM.GetModelCreditType(chatReq.Model)
		trialCredit := userQuotaInfo.FeatureUsed.Total.WebCodeFreeTrial - userQuotaInfo.FeatureUsed.Used.WebCodeFreeTrial

		// 根据模型积分类型计算剩余积分
		var remainCredit int
		if modelCreditType == userquota.CreditTypeBasic {
			// 基础积分模型
			truthRemain := userQuotaInfo.GetRemain(userquota.CreditTypeBasic)
			remainCredit = truthRemain + userQuotaInfo.GetExtraRemain(userquota.CreditTypeBasic)
			if remainCredit <= 0 && trialCredit <= 0 {
				return api.ErrorBasicUserQuotaInsufficient("Error Remain basic Credits Insufficient")
			}
		} else {
			// 高级积分模型
			truthRemain := userQuotaInfo.GetRemain(userquota.CreditTypeAdvanced)
			if userQuotaInfo.AdvancedCredit.Used >= userQuotaInfo.UnlimitedAdvancedCreditLimit {
				truthRemain = 0
			}
			remainCredit = truthRemain + userQuotaInfo.GetExtraRemain(userquota.CreditTypeAdvanced)
			if remainCredit <= 0 && trialCredit <= 0 {
				if userQuotaInfo.AdvancedCredit.Used >= userQuotaInfo.UnlimitedAdvancedCreditLimit {
					return api.ErrorAdvanceUserQuotaExceedsTarget("User quota exceeded target value")
				}
				return api.ErrorAdvanceUserQuotaInsufficient("Error Remain advance Credits Insufficient")
			}
		}
		if in.Chat.ChatHistoryID == "" {
			var err error
			if len(in.Chat.Prompt) > 0 {
				chatReq.ChatHistoryID, err = s.chatHistoryBiz.CreateHistoryByWisebaseID(ctx, userInfo.UserID, in.WisebaseID, nil, in.Chat.Prompt[0].Text)
			} else {
				chatReq.ChatHistoryID, err = s.chatHistoryBiz.CreateHistoryByWisebaseID(ctx, userInfo.UserID, in.WisebaseID, nil, "")
			}
			if err != nil {
				return api.ErrorGetOneFailed("Failed to create chat history for the given wisebase: %v", err.Error())
			}
			// 向客户端发送会话创建成功的消息
			s.chatWebBiz.SendDataToClient(ctx, &biz.ChatRequest{ChatHistoryID: chatReq.ChatHistoryID}, notifyCh, biz.CreatedConversation(""), nil)
		}
		// 创建锁通道并与LLM进行聊天
		chatResp, err := s.chatWebBiz.ChatWithLLM(ctx, chatReq, notifyCh)
		if chatResp == nil {
			return v1.ErrorChatWithLlmFailed(err.Error())
		}
		isFail := false
		if err != nil {
			isFail = true
		}
		quotaErr := s.userQuota(ctx, trialCredit, remainCredit, userID, chatReq, chatResp, notifyCh, userQuotaInfo, isFail)
		if err != nil {
			if quotaErr != nil {
				return v1.ErrorChatWithLlmFailed("chat err: %v,quotaErr: %v", err.Error(), quotaErr.Error())
			}
			return v1.ErrorChatWithLlmFailed(err.Error())
		}

		return nil
	})
	return nil
}

func (s *ChatWebService) chatFree(ctx context.Context, in *v1.WebChatRequest, chatReq *biz.ChatRequest, userInfo *ginutil.UserInfo, notifyCh chan []byte) *errors.Error {
	if in.Chat.ChatHistoryID == "" {
		var err error
		if len(in.Chat.Prompt) > 0 {
			chatReq.ChatHistoryID, err = s.chatHistoryBiz.CreateHistoryByWisebaseID(ctx, userInfo.UserID, in.WisebaseID, nil, in.Chat.Prompt[0].Text)
		} else {
			chatReq.ChatHistoryID, err = s.chatHistoryBiz.CreateHistoryByWisebaseID(ctx, userInfo.UserID, in.WisebaseID, nil, "")
		}
		if err != nil {
			return api.ErrorGetOneFailed("Failed to create chat history for the given wisebase: %v", err.Error())
		}
		// 向客户端发送会话创建成功的消息
		s.chatWebBiz.SendDataToClient(ctx, &biz.ChatRequest{ChatHistoryID: chatReq.ChatHistoryID}, notifyCh, biz.CreatedConversation(""), nil)
	}

	_, err := s.chatWebBiz.ChatWithLLM(ctx, chatReq, notifyCh)
	if err != nil {
		return v1.ErrorChatWithLlmFailed(err.Error())
	}

	// 向客户端发送积分使用信息
	data := siderllm.ChatStreamUserQuotaData{
		Type: siderllm.ChatStreamTypeUserQuota,
		UserQuota: userquota.QuotaInfoResult{
			FeatureUsed: userquota.FeatureUsedLimit{},
			BasicCredit: userquota.CreditInfo{
				Used: 0,
			},
			AdvancedCredit: userquota.CreditInfo{
				Used: 0,
			},
		},
	}
	s.chatWebBiz.SendDataToClient(ctx, chatReq, notifyCh, data, nil)
	return nil
}

func (s *ChatWebService) FunctionCallResult(ctx context.Context, in *v1.FunctionCallResultRequest) (*v1.FunctionCallResultResponse, error) {
	err := s.chatWebBiz.FunctionCallResult(ctx, in)
	if err != nil {
		return nil, err
	}
	return &v1.FunctionCallResultResponse{
		Success: true,
	}, nil
}

// 上面是router调用的service方法，下面是拆分的service方法
func (s *ChatWebService) chatToRequest(ctx context.Context, req *biz.ChatUserRequest) (*biz.ChatRequest, *errors.Error) {
	// 检查并设置请求参数
	chatRequest, err := s.chatWebBiz.CheckAndSetRequest(ctx, req)
	if err != nil {
		return nil, err
	}
	// glm4.5不使用工具
	if req.Model == biz.ModelNameGLM4Dot5 {
		return chatRequest, nil
	}
	// 如果请求中包含工具参数且自动工具列表不为空
	if chatRequest.RequestMessage.ToolsParam != nil && len(chatRequest.RequestMessage.ToolsParam.Auto) > 0 {
		// 查询工具列表
		tools, _ := s.toolBiz.Tools(ctx, chatRequest.RequestMessage.ToolsParam.Auto)
		chatRequest.Tools = tools
	}
	return chatRequest, nil
}

func (s *ChatWebService) getChatUserRequest(ctx context.Context, in *v1.ChatRequest, userID int64, wisebaseID string) (*biz.ChatUserRequest, error) {
	resources, err := s.wisebaseClient.GetResources(ctx, userID, wisebaseID)
	if err != nil {
		return nil, err
	}

	// 创建一个 map 用于去重，key 是文件 ID
	fileIDMap := make(map[string]string)

	// 添加 resources 中的文件 ID
	if resources != nil && resources.Items != nil {
		for _, resource := range resources.Items {
			if resource.ID != "" {
				fileIDMap[resource.File.SiderFileId] = resource.ID
			}
		}
	}

	// 添加 in.File 中的文件 ID（并集操作）
	if in.File != nil {
		for _, v := range in.File {
			if v.Type == "file" && v.FileID != "" {
				fileIDMap[v.FileID] = v.LibraryFileID
			}
		}
	}

	//获得附件内容
	var fileText string
	var fileErr error
	var siderFileIds map[string]struct{}
	if len(fileIDMap) > 0 {
		fileText, siderFileIds, fileErr = s.chatFileBiz.GetFileContent(ctx, fileIDMap, userID, wisebaseID)
		if fileErr != nil {
			return nil, fileErr
		}
		//即使有多个提示词也会一一拼接附件内容
		for _, item := range in.PromptTemplates {
			item.Attributes["files"] = fileText
		}
	}

	loc, err := time.LoadLocation("America/New_York")
	if err != nil {
		return nil, err
	}

	// 获取当前美国时间
	currentTime := time.Now().In(loc)

	for _, item := range in.PromptTemplates {
		item.Attributes["time"] = currentTime.Format("2006-01-02 15:04:05")
	}

	// 创建并初始化业务层请求对象
	req := &biz.ChatUserRequest{
		Model:         in.Model,         // 使用的AI模型
		ChatHistoryID: in.ChatHistoryID, //历史聊天记录id
		ParentID:      in.ParentID,      //父消息id
		// 转换提示模板列表
		ImageSiderFileID: siderFileIds,
		PromptTemplates: lo.Map(in.PromptTemplates, func(item *v1.PromptTemplate, index int) documentmodel.PromptTemplate {
			return documentmodel.PromptTemplate{
				Attributes: item.Attributes, // 模板属性
				Key:        item.Key,        // 模板键
				Scene:      item.Scene,      // 使用场景
				Platform:   item.Platform,   // 平台信息
			}
		}),
		FunctionCall: in.FunctionCall,
		Prompt:       in.Prompt,
		UserID:       int(userID),
	}

	// 处理引用内容（如果存在）
	if in.Quote != nil {
		req.Quote = &documentmodel.Quote{
			Type:    documentmodel.ContentQuoteType(in.Quote.Type), // 引用类型
			Content: in.Quote.Content,                              // 引用内容
		}
	}

	// 处理工具参数（如果存在）
	if in.ToolsParam != nil {
		req.ToolsParam = &siderllm.ToolsParam{
			Auto:  in.ToolsParam.Auto,  // 是否自动使用工具
			Force: in.ToolsParam.Force, // 是否强制使用工具
		}
	}
	return req, nil
}

func (s *ChatWebService) chat(ctx context.Context, req any, notifyCh chan []byte, f func() *errors.Error) {
	// 序列化请求参数用于日志记录
	var args []byte
	if req != nil {
		args, _ = json.Marshal(req)
	}
	// 记录开始时间，用于计算请求延迟
	startTime := time.Now()
	// 延迟函数用于捕获和处理panic
	defer func() {
		if err := recover(); err != nil {
			//将panic转换为错误响应
			panicErr := v1.ErrorChatWithLlmFailed(fmt.Sprintf("%v", err))
			msg := &v1.ChatMessageResp{
				Type:  biz.ChatMessageTypeErr,
				Error: panicErr.GetReason(),
				Code:  panicErr.GetCode(),
			}
			//序列化错误消息并发送到通知通道
			bs, _ := json.Marshal(msg)
			notifyCh <- bs

			// 记录panic详情到日志
			slog.ErrorContext(ctx, "chat panic recovered",
				slog.String("args", string(args)),
				slog.String("kind", "sse"),
				slog.Float64("latency", float64(time.Since(startTime).Milliseconds())),
			)
		}
	}()
	// 执行聊天逻辑函数
	if err := f(); err != nil {
		// 创建错误响应消息
		msg := &v1.ChatMessageResp{
			Type:  biz.ChatMessageTypeErr,
			Error: err.GetReason(),
			Code:  err.GetCode(),
		}
		// 序列化错误消息并发送到通知通道
		bs, _ := json.Marshal(msg)
		notifyCh <- bs

		// 根据错误类型选择不同的日志级别
		if err.Is(api.ErrorTooManyRequest("")) {
			// 请求过多错误使用警告级别日志
			slog.WarnContext(ctx, "chat failed",
				slog.String("reason", err.GetReason()),
				slog.Int("code", int(err.GetCode())),
				slog.String("args", string(args)),
				slog.String("stack", err.GetMessage()),
				slog.Any("meta", err.GetMetadata()),
				slog.String("kind", "sse"),
				slog.Float64("latency", float64(time.Since(startTime).Milliseconds())),
			)
		} else {
			// 其他错误使用错误级别日志
			slog.ErrorContext(ctx, "chat failed",
				slog.String("reason", err.GetReason()),
				slog.Int("code", int(err.GetCode())),
				slog.String("args", string(args)),
				slog.String("stack", err.GetMessage()),
				slog.Any("meta", err.GetMetadata()),
				slog.String("kind", "sse"),
				slog.Float64("latency", float64(time.Since(startTime).Milliseconds())),
			)
		}
	}
}

// error 已经被 api 错误状态吗包裹
func (s *ChatWebService) getUserId(ctx context.Context) (int64, string, error) {
	userInfo := ginutil.GetCurrentUserInfo(ctx)
	return int64(userInfo.UserID), userInfo.UserHashID, nil
}

func (s *ChatWebService) userQuota(ctx context.Context, trialCredit, remainCredit int, userID int64, chatReq *biz.ChatRequest, chatResp *siderllm.MessageResp, notifyCh chan []byte, userQuotaInfo *userquota.QuotaInfoResult, isFail bool) *errors.Error {
	if trialCredit > 0 {
		if isFail {
			return nil
		}
		// 使用新的 context 进行试用积分扣费操作

		trialQuotaErr := s.userQM.DeductTrialQuotaChat(
			ctx,
			int(userID),
		)
		if trialQuotaErr != nil {
			return trialQuotaErr
		}

		// 更新聊天消息中的积分使用信息
		if chatReq.AIMessage.ID != "" {
			modelCreditType := s.userQM.GetModelCreditType(chatReq.Model)
			if saveErr := s.chatWebBiz.UpdateCreditUsage(ctx, chatReq.AIMessage.ID, &biz.ChatMessage{
				ChatConversationID: chatReq.AIMessage.ChatConversationID,
				CreditType:         modelCreditType, // 使用模型对应的积分类型
				CreditCount:        -1 - userQuotaInfo.FeatureUsed.Used.WebCodeFreeTrial,
				IsTrialCredit:      true, // 通过这个字段标识是试用次数
			}); saveErr != nil {
				s.chatWebBiz.SendDataToClient(ctx, chatReq, notifyCh, nil, api.ErrorUpdateOneFailed("update chat message credit token failed", saveErr))
			}

			// 向客户端发送积分使用信息
			data := siderllm.ChatStreamUserQuotaData{
				Type: siderllm.ChatStreamTypeUserQuota,
				UserQuota: userquota.QuotaInfoResult{
					FeatureUsed: userquota.FeatureUsedLimit{
						Total: userquota.FeatureLimit{
							WebCodeFreeTrial: userQuotaInfo.FeatureUsed.Total.WebCodeFreeTrial,
						},
						Used: userquota.FeatureLimit{
							WebCodeFreeTrial: userQuotaInfo.FeatureUsed.Used.WebCodeFreeTrial + 1,
						},
					},
				},
			}
			s.chatWebBiz.SendDataToClient(ctx, chatReq, notifyCh, data, nil)
		}
	} else {

		if isFail {
			if chatResp.FullContents == nil {
				return nil
			}
			inputText, outputText := s.buildInputAndOutputText(chatReq, chatResp)
			chatResp.Usage.PromptTokens, chatResp.Usage.CompletionTokens = s.userQM.TikToken(inputText, outputText, chatReq.Model)
		}
		cachedTokens := 0
		CacheCreationTokens := 0

		//读取缓存token
		if chatResp.Usage.PromptTokensDetails != nil && chatResp.Usage.PromptTokensDetails.CachedTokens != 0 {
			cachedTokens = chatResp.Usage.PromptTokensDetails.CachedTokens
		}
		//创建缓存token（只有claude会有）
		if chatResp.Usage.PromptTokensDetails != nil && chatResp.Usage.PromptTokensDetails.CacheCreationTokens != 0 {
			CacheCreationTokens = chatResp.Usage.PromptTokensDetails.CacheCreationTokens
		}

		chatResp.Usage.PromptTokens = chatResp.Usage.PromptTokens - cachedTokens - CacheCreationTokens

		if chatResp.Usage.PromptTokensDetails != nil { //只有openai 不是nil
			cachedTokens = chatResp.Usage.PromptTokensDetails.CachedTokens
		}

		// 扣除用户积分
		// 聊天的积分计算公式：(模型输入成本+模型输出成本+输入Cache成本) × 系数) ÷ 基础积分的成本值 + 累计积分
		// 使用新的 context 进行扣费操作，避免使用可能已取消的原始 ctx
		result, quotaErr := s.userQM.DeductQuotaChat(
			ctx,
			int(userID),
			chatReq.Model,
			chatResp.Usage.PromptTokens,
			chatResp.Usage.CompletionTokens,
			cachedTokens,
			CacheCreationTokens,
			remainCredit,
		)
		if quotaErr != nil {
			return quotaErr
		}
		quotaCtx := context.Background()

		// 更新聊天消息中的积分使用信息
		if chatReq.AIMessage.ID != "" {
			modelCreditType := s.userQM.GetModelCreditType(chatReq.Model)
			if saveErr := s.chatWebBiz.UpdateCreditUsage(quotaCtx, chatReq.AIMessage.ID, &biz.ChatMessage{
				ChatConversationID: chatReq.AIMessage.ChatConversationID,
				CreditType:         modelCreditType, // 使用模型对应的积分类型
				CreditCount:        result.IntegerPart,
			}); saveErr != nil {
				s.chatWebBiz.SendDataToClient(ctx, chatReq, notifyCh, nil, api.ErrorUpdateOneFailed("update chat message credit token failed", saveErr))
			}

			// 向客户端发送积分使用信息
			data := siderllm.ChatStreamUserQuotaData{
				Type: siderllm.ChatStreamTypeUserQuota,
				UserQuota: userquota.QuotaInfoResult{
					FeatureUsed: userquota.FeatureUsedLimit{
						Total: userquota.FeatureLimit{
							WebCodeFreeTrial: userQuotaInfo.FeatureUsed.Total.WebCodeFreeTrial,
						},
						Used: userquota.FeatureLimit{
							WebCodeFreeTrial: userQuotaInfo.FeatureUsed.Used.WebCodeFreeTrial,
						},
					},
				},
			}
			// 根据积分类型设置对应的积分使用信息
			if modelCreditType == userquota.CreditTypeBasic {
				data.UserQuota.BasicCredit = userquota.CreditInfo{
					Used: result.IntegerPart,
				}
			} else {
				data.UserQuota.AdvancedCredit = userquota.CreditInfo{
					Used: result.IntegerPart,
				}
			}
			s.chatWebBiz.SendDataToClient(ctx, chatReq, notifyCh, data, nil)
		}
	}
	return nil
}

func (s *ChatWebService) buildInputAndOutputText(chatReq *biz.ChatRequest, chatResp *siderllm.MessageResp) (string, string) {
	var inputText strings.Builder
	var outputText strings.Builder

	// 添加历史消息列表中的文本内容
	for _, message := range chatReq.MessageList {
		if message.MultiContent != nil {
			for _, content := range message.MultiContent {
				if content.Text != "" {
					inputText.WriteString(content.Text)
				}
			}
		}
	}

	// 添加当前请求消息中的文本内容
	if chatReq.RequestMessage != nil && chatReq.RequestMessage.MultiContent != nil {
		for _, content := range chatReq.RequestMessage.MultiContent {
			if content.Type == documentmodel.ContentTypeText && content.Text != "" {
				inputText.WriteString(content.Text)
			}
		}
	}

	// 添加模型输出内容
	if chatResp.FullContents != nil {
		for _, content := range chatResp.FullContents {
			if content.Text != "" {
				outputText.WriteString(content.Text)
			}
			if content.ReasoningContent != "" {
				outputText.WriteString(content.ReasoningContent)
			}
		}
	}

	return inputText.String(), outputText.String()
}
